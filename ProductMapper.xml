<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.mapper.ProductMapper">

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        p.commodity_id,
        p.commodity_name,
        p.class_id,
        p.class_name,
        p.brand_id,
        p.brand_name,
        p.img,
        p.weight,
        p.type,
        p.delivery_fee,
        p.price,
        p.sell_price,
        p.details_url,
        p.detail_image,
        p.sixnight,
        p.store_state,
        p.create_time,
        p.update_time
    </sql>

    <!-- 基础查询条件 -->
    <sql id="baseConditions">
        <where>
            <if test="storeId != null and storeId != ''">
                AND p.store_id = #{storeId}
            </if>
            <if test="classId != null and classId != 0">
                AND p.class_id = #{classId}
            </if>
            <if test="brandId != null and brandId != 0">
                AND p.brand_id = #{brandId}
            </if>
            <if test="state != null and state != 0">
                <choose>
                    <when test="state == 1">
                        AND p.store_state = 1
                    </when>
                    <when test="state == 2">
                        AND p.store_state = 0
                    </when>
                </choose>
            </if>
        </where>
    </sql>

    <!-- 单关键词搜索（原有方法） -->
    <select id="selectByCondition" parameterType="com.example.entity.ProductSearchCondition" 
            resultType="com.example.entity.Product">
        SELECT 
            <include refid="baseColumns"/>
        FROM products p
        <include refid="baseConditions"/>
        <if test="productName != null and productName != ''">
            AND (
                p.commodity_name LIKE CONCAT('%', #{productName}, '%')
                OR p.details LIKE CONCAT('%', #{productName}, '%')
            )
        </if>
        ORDER BY p.update_time DESC
    </select>

    <!-- 多关键词搜索（新增方法） -->
    <select id="selectByMultiKeywords" parameterType="com.example.entity.ProductSearchCondition" 
            resultType="com.example.entity.Product">
        SELECT 
            <include refid="baseColumns"/>
        FROM products p
        <include refid="baseConditions"/>
        <if test="searchKeywords != null and searchKeywords.size() > 0">
            AND (
                <foreach collection="searchKeywords" item="keyword" separator=" AND ">
                    (
                        p.commodity_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.details LIKE CONCAT('%', #{keyword}, '%')
                        OR p.class_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.brand_name LIKE CONCAT('%', #{keyword}, '%')
                    )
                </foreach>
            )
        </if>
        ORDER BY p.update_time DESC
    </select>

    <!-- 多关键词搜索（OR逻辑版本） -->
    <select id="selectByMultiKeywordsOr" parameterType="com.example.entity.ProductSearchCondition" 
            resultType="com.example.entity.Product">
        SELECT 
            <include refid="baseColumns"/>
        FROM products p
        <include refid="baseConditions"/>
        <if test="searchKeywords != null and searchKeywords.size() > 0">
            AND (
                <foreach collection="searchKeywords" item="keyword" separator=" OR ">
                    (
                        p.commodity_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.details LIKE CONCAT('%', #{keyword}, '%')
                        OR p.class_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.brand_name LIKE CONCAT('%', #{keyword}, '%')
                    )
                </foreach>
            )
        </if>
        ORDER BY p.update_time DESC
    </select>

    <!-- 多关键词搜索（全文搜索版本，需要MySQL 5.7+） -->
    <select id="selectByMultiKeywordsFullText" parameterType="com.example.entity.ProductSearchCondition" 
            resultType="com.example.entity.Product">
        SELECT 
            <include refid="baseColumns"/>,
            MATCH(p.commodity_name, p.details) AGAINST(#{searchText} IN NATURAL LANGUAGE MODE) as relevance_score
        FROM products p
        <include refid="baseConditions"/>
        <if test="searchText != null and searchText != ''">
            AND MATCH(p.commodity_name, p.details) AGAINST(#{searchText} IN NATURAL LANGUAGE MODE)
        </if>
        ORDER BY relevance_score DESC, p.update_time DESC
    </select>

    <!-- 统计搜索结果数量 -->
    <select id="countByMultiKeywords" parameterType="com.example.entity.ProductSearchCondition" 
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM products p
        <include refid="baseConditions"/>
        <if test="searchKeywords != null and searchKeywords.size() > 0">
            AND (
                <foreach collection="searchKeywords" item="keyword" separator=" AND ">
                    (
                        p.commodity_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.details LIKE CONCAT('%', #{keyword}, '%')
                        OR p.class_name LIKE CONCAT('%', #{keyword}, '%')
                        OR p.brand_name LIKE CONCAT('%', #{keyword}, '%')
                    )
                </foreach>
            )
        </if>
    </select>

</mapper>
