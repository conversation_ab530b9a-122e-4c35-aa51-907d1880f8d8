/**
 * @姓名 杨强
 * @版本号 1.1.4
 * @日期 2019/6/25
 */
package com.example.waterstationbuyproducer.szmb.controller.yq;


import com.example.waterstationbuyproducer.szmb.service.yq.NewProductService;
import com.example.waterstationbuyproducer.szmb.service.yq.NewSelectProductService;
import com.example.waterstationbuyproducer.szmb.vo.yq.AddShop;
import com.example.waterstationbuyproducer.szmb.vo.yq.AddShopList;
import com.example.waterstationbuyproducer.util.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.executor.ReuseExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Random;
import java.util.Scanner;

@RestController
@RequestMapping("szmb/newinsertproductcontroller")
@Api(value = "szmb/newinsertproductcontroller/", description = "字典商品")
public class NewInsertProductController {
    @Autowired
    private NewProductService newProductService;
    @Autowired
    private NewSelectProductService newSelectProductService;


    @GetMapping("updateBatchDeliveryFee")
    @ApiOperation(value = "更新全部配送费", httpMethod = "POST", response = String.class, notes = "更新全部配送费")
    public ResultBean updateBatchDeliveryFee(@RequestParam("deliveryfee") String deliveryfee){
        return newProductService.updateBatchDeliveryFee(deliveryfee);
    }

    @PostMapping("insertproduct")
    @ApiOperation(value = "添加商品", httpMethod = "POST", response = String.class, notes = "添加商品")
    public ResultBean insertProduct(@RequestBody AddShop shop){
        return newProductService.insertProduct(shop);
    }


    @PostMapping("selectproductall")
    @ApiOperation(value = "查看全部字典商品", httpMethod = "POST", response = String.class, notes = "查看全部字典商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classId",value = "分类id",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "index",value = "下标",dataType = "Integer",paramType = "query")
    })
    public ResultBean selectProductAll(Long classId,Integer index){
        return newSelectProductService.selectProduct(classId,index);
    }


    @PostMapping("selectproductdeatil")
    @ApiOperation(value = "查看字典主商品的详情", httpMethod = "POST", response = String.class, notes = "查看字典主商品的详情")
    public ResultBean selectProductDeatil(Long spuId){
        return newSelectProductService.selectShopSpu(spuId);
    }

    @PostMapping("selectproductmodle")
    @ApiOperation(value = "查看字典子商品详情", httpMethod = "POST", response = String.class, notes = "查看字典子商品详情")
    public ResultBean selectProductModle(Long skuId){
        return newSelectProductService.selectShopSkuDeatil(skuId);
    }



    @PostMapping("selectproductname")
    @ApiOperation(value = "根据名称查看商品信息", httpMethod = "POST", response = String.class, notes = "根据名称查看商品信息")
    public ResultBean selectProductName(String name){
        return newSelectProductService.selectListProduct(name);
    }


    @PostMapping("selectclassbyname")
    @ApiOperation(value = "模糊查询分类", httpMethod = "POST", response = String.class, notes = "模糊查询分类")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId",value = "店铺id",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "classId",value = "分类id",dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "name",value = "名称",dataType = "String",paramType = "query")
    })
    public ResultBean selectClassByName(Long storeId,Long classId,String name){
        return newProductService.insertClassAndBrand(storeId,classId,name);
    }


    @PostMapping("insertproductlist")
    @ApiOperation(value = "批量商家产品库商品", httpMethod = "POST", response = String.class, notes = "批量商家产品库商品")
    public ResultBean insertProductList(@RequestBody AddShopList addShopList){
        return newProductService.insertProductList(addShopList);
    }

    @PostMapping("selectstoreproduct")
    @ApiOperation(value = "查看商家选中的字典商品", httpMethod = "POST", response = String.class, notes = "查看商家选中的字典商品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId",value = "店铺id",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "classId",value = "分类id",dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "index",value = "下标",dataType = "Integer",paramType = "query")
    })
    public ResultBean selectStoreProduct(Long storeId,Long classId,Integer index){
        return newSelectProductService.selectProductByStoreId(storeId,classId,index);
    }

    @PostMapping("selectstoreproductpc")
    @ApiOperation(value = "查看商家选中的字典商品(pc)", httpMethod = "POST", response = String.class, notes = "查看商家选中的字典商品(pc)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId",value = "店铺id",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "classId",value = "分类id",dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "index",value = "下标",dataType = "Integer",paramType = "query"),
            @ApiImplicitParam(name = "productName",value = "名称",dataType = "Integer",paramType = "query"),
            @ApiImplicitParam(name = "state",value = "0全部1上架2下架",dataType = "Integer",paramType = "query"),
            @ApiImplicitParam(name = "brandId",value = "品牌id",dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "searchKeywords",value = "多关键词搜索JSON数组",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "isMultiKeywordSearch",value = "是否多关键词搜索(1是0否)",dataType = "Integer",paramType = "query"),
    })
    public ResultBean selectStoreProductPc(Long storeId,Long classId,Integer index,String productName,Integer state,Long brandId,
                                          String searchKeywords, Integer isMultiKeywordSearch){
        return newSelectProductService.selectProductByStoreIdPc(storeId,classId,index,productName,state,brandId,searchKeywords,isMultiKeywordSearch);
    }

    @PostMapping("selectclasssname")
    @ApiOperation(value = "查看分类名称", httpMethod = "POST", response = String.class, notes = "查看分类名称")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeId",value = "店铺id",dataType = "String",paramType = "query"),
            @ApiImplicitParam(name = "classId",value = "分类id",dataType = "Long",paramType = "query"),
            @ApiImplicitParam(name = "className",value = "分类名称",dataType = "String",paramType = "query")
    })
    public ResultBean selectClassName(Long storeId,String className){
        return newProductService.insertClassAndBrandNew(storeId,0L,className);
    }

    @PostMapping("selectstorelable")
    @ApiOperation(value = "查看标签", httpMethod = "POST", response = String.class, notes = "查看标签")
    public ResultBean selectStoreLable(){
        return newSelectProductService.selectProductLable();
    }

    /**
     * 查询该店铺的分类和品牌
     * @param storeId
     * @return
     */
    @PostMapping("selectstoreallbrand")
    @ApiOperation(value = "查询该店铺的分类和品牌", httpMethod = "POST", response = String.class, notes = "查询该店铺的分类和品牌")
    @ApiImplicitParam(name = "storeId",value = "店铺id",dataType = "String",paramType = "query")
    public ResultBean selectStoreAllBrand(Long storeId){
        return newSelectProductService.selectStoreAllBrand(storeId);
    }
}
