package com.example.waterstationbuyproducer.dao;

import com.example.waterstationbuyproducer.entity.SzmCProduct;
import com.example.waterstationbuyproducer.entity.SzmCProductNew;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.net.Inet4Address;
import java.util.List;
import java.util.Map;


@Repository
public interface SzmCProductNewMapper {
    int deleteByPrimaryKey(Long productId);

    int insert(SzmCProductNew record);

    SzmCProductNew selectByPrimaryKey(Long productId);

    List<SzmCProductNew> selectAll();

    int updateByPrimaryKey(SzmCProductNew record);

    SzmCProductNew selectByClassId(@Param("classId") Long classId, @Param("productName") String productName);


    List<SzmCProductNew> selectByClasssAll(@Param("classId") Long classId);

    List<SzmCProductNew> selectByName(@Param("name") String name);

    List<SzmCProductNew> selectByClassName(@Param("classId") Long classId, @Param("name") String name);

    List<SzmCProductNew> selectProductAll();

    SzmCProduct selectByProductName(@Param("name") String name);

    List<SzmCProductNew> selectProductCity();

    List<SzmCProductNew> selectCityCodeList(@Param("cityCode") String cityCode);


    List<SzmCProductNew> selectProductList(@Param("classId") Long classId,
                                           @Param("name") String name,
                                           @Param("state") Integer state,
                                           @Param("storeId") Long storeId,
                                           @Param("brandId") Long brandId);

    List<SzmCProductNew> selectProductListCs(@Param("classId") Long classId,
                                           @Param("name") String name,
                                           @Param("state") Integer state,
                                           @Param("storeId") Long storeId,
                                           @Param("brandId") Long brandId);

    List<SzmCProductNew> selectProductListAll(@Param("classId") Long classId,
                                             @Param("name") String name,
                                             @Param("state") Integer state,
                                             @Param("storeId") Long storeId,
                                             @Param("brandId") Long brandId);



    Integer selectProductCount(@Param("productId") Long storeId,
                               @Param("classId") Long classId,
                               @Param("productName") String productName);

    Integer selectProductTypeCount(@Param("productName") String productName);

    SzmCProductNew selectOtherByPrimaryKey(Long productId);

    Integer updateBatchDeliveryFee(@Param("deliveryfee") String deliveryfee);

    List<SzmCProductNew> selectByBrandAndTitleLikeIn(@Param("brandid") Integer brandid, @Param("specification") List<String> specification);

    /**
     * 搜索产品 - 用于订单来源关联产品查询
     */
    List<Map<String, Object>> searchProductsForOrderSource(@Param("params") Map<String, Object> params);

    /**
     * 获取产品详情 - 用于订单来源关联产品查询
     */
    Map<String, Object> getProductDetailForOrderSource(@Param("productId") Long productId, @Param("storeId") Long storeId);

    /**
     * 获取产品总数 - 用于分页
     */
    Integer countProductsForOrderSource(@Param("params") Map<String, Object> params);
}