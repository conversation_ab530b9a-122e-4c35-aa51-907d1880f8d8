/**
 * 商品库多关键词搜索后端实现示例
 * 接口地址: /szmb/newinsertproductcontroller/selectstoreproductpc
 */

@RestController
@RequestMapping("/szmb/newinsertproductcontroller")
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * 查询店铺商品（支持多关键词搜索）
     */
    @PostMapping("/selectstoreproductpc")
    public ResponseResult selectStoreProductPc(@RequestBody ProductSearchRequest request) {
        try {
            // 验证必要参数
            if (StringUtils.isEmpty(request.getStoreId())) {
                return ResponseResult.error("店铺ID不能为空");
            }

            // 构建查询条件
            ProductSearchCondition condition = buildSearchCondition(request);
            
            // 执行查询
            PageResult<Product> result = productService.searchProducts(condition);
            
            return ResponseResult.success(result);
            
        } catch (Exception e) {
            log.error("查询商品失败", e);
            return ResponseResult.error("查询失败");
        }
    }

    /**
     * 构建搜索条件
     */
    private ProductSearchCondition buildSearchCondition(ProductSearchRequest request) {
        ProductSearchCondition condition = new ProductSearchCondition();
        
        condition.setStoreId(request.getStoreId());
        condition.setClassId(request.getClassId());
        condition.setBrandId(request.getBrandId());
        condition.setState(request.getState());
        condition.setPageIndex(request.getIndex());
        
        // 处理搜索关键词
        if (request.getIsMultiKeywordSearch() == 1 && 
            !StringUtils.isEmpty(request.getSearchKeywords())) {
            
            // 多关键词搜索
            try {
                List<String> keywords = JSON.parseArray(request.getSearchKeywords(), String.class);
                condition.setSearchKeywords(keywords);
                condition.setMultiKeywordSearch(true);
                
                log.info("多关键词搜索: {}", keywords);
                
            } catch (Exception e) {
                log.warn("解析多关键词失败，使用单关键词搜索: {}", request.getProductName());
                condition.setProductName(request.getProductName());
                condition.setMultiKeywordSearch(false);
            }
            
        } else {
            // 单关键词搜索（保持原有逻辑）
            condition.setProductName(request.getProductName());
            condition.setMultiKeywordSearch(false);
        }
        
        return condition;
    }
}

/**
 * 请求参数类
 */
@Data
public class ProductSearchRequest {
    private String storeId;
    private Integer classId;
    private Integer brandId;
    private Integer state;
    private Integer index;
    private String productName;           // 原有参数，保持兼容性
    private String searchKeywords;        // 多关键词JSON数组字符串
    private Integer isMultiKeywordSearch; // 是否为多关键词搜索标识
}

/**
 * 搜索条件类
 */
@Data
public class ProductSearchCondition {
    private String storeId;
    private Integer classId;
    private Integer brandId;
    private Integer state;
    private Integer pageIndex;
    private String productName;
    private List<String> searchKeywords;
    private boolean multiKeywordSearch;
}

/**
 * 服务层实现
 */
@Service
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Override
    public PageResult<Product> searchProducts(ProductSearchCondition condition) {
        
        // 设置分页
        PageHelper.startPage(condition.getPageIndex(), 20);
        
        List<Product> products;
        
        if (condition.isMultiKeywordSearch() && 
            condition.getSearchKeywords() != null && 
            !condition.getSearchKeywords().isEmpty()) {
            
            // 多关键词搜索
            products = productMapper.selectByMultiKeywords(condition);
            
        } else {
            // 单关键词搜索（原有逻辑）
            products = productMapper.selectByCondition(condition);
        }
        
        PageInfo<Product> pageInfo = new PageInfo<>(products);
        
        return new PageResult<>(pageInfo.getList(), pageInfo.getTotal());
    }
}

/**
 * Mapper接口
 */
@Mapper
public interface ProductMapper {
    
    /**
     * 单关键词搜索（原有方法）
     */
    List<Product> selectByCondition(ProductSearchCondition condition);
    
    /**
     * 多关键词搜索（新增方法）
     */
    List<Product> selectByMultiKeywords(ProductSearchCondition condition);
}
