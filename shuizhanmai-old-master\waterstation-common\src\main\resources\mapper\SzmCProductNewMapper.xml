<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.waterstationbuyproducer.dao.SzmCProductNewMapper">
  <resultMap id="BaseResultMap" type="com.example.waterstationbuyproducer.entity.SzmCProductNew">
    <id column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="product_classify_id" jdbcType="BIGINT" property="productClassifyId" />
    <result column="product_title" jdbcType="VARCHAR" property="productTitle" />
    <result column="product_service" jdbcType="VARCHAR" property="productService" />
    <result column="is_alive" jdbcType="INTEGER" property="isAlive" />
    <result column="product_order_num" jdbcType="INTEGER" property="productOrderNum" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="r_1" jdbcType="VARCHAR" property="r1" />
    <result column="r_2" jdbcType="VARCHAR" property="r2" />
    <result column="r_3" jdbcType="VARCHAR" property="r3" />
    <result column="r_4" jdbcType="VARCHAR" property="r4" />
    <result column="r_5" jdbcType="VARCHAR" property="r5" />
    <result column="product_describe" jdbcType="VARCHAR" property="productDescribe" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="deliveryfee" jdbcType="VARCHAR" property="deliveryfee" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="sellprice" jdbcType="DECIMAL" property="sellprice" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="sixnight" jdbcType="VARCHAR" property="sixnight" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from szm_c_product_new
    where product_id = #{productId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.example.waterstationbuyproducer.entity.SzmCProductNew">
    insert into szm_c_product_new (product_id, create_time, update_time, 
      store_id, product_classify_id, product_title, 
      product_service, is_alive, product_order_num, 
      `state`, r_1, r_2, r_3, 
      r_4, r_5, product_describe,weight,type,deliveryfee,price,sellprice,spec,sixnight
      )
    values (#{productId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{storeId,jdbcType=VARCHAR}, #{productClassifyId,jdbcType=BIGINT}, #{productTitle,jdbcType=VARCHAR}, 
      #{productService,jdbcType=VARCHAR}, #{isAlive,jdbcType=INTEGER}, #{productOrderNum,jdbcType=INTEGER}, 
      #{state,jdbcType=INTEGER}, #{r1,jdbcType=VARCHAR}, #{r2,jdbcType=VARCHAR}, #{r3,jdbcType=VARCHAR}, 
      #{r4,jdbcType=VARCHAR}, #{r5,jdbcType=VARCHAR}, #{productDescribe,jdbcType=LONGVARCHAR}
      ,#{weight,jdbcType=DECIMAL},#{type,jdbcType=INTEGER},#{deliveryfee,jdbcType=VARCHAR},#{price,jdbcType=DECIMAL},#{sellprice,jdbcType=DECIMAL},#{spec,jdbcType=VARCHAR},#{sixnight,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.example.waterstationbuyproducer.entity.SzmCProductNew">
    update szm_c_product_new
    <trim prefix="set" suffixOverrides=",">
      <if test="createTime!=null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
      <if test="updateTime!=null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
      <if test="storeId!=null">store_id = #{storeId,jdbcType=VARCHAR},</if>
      <if test="productClassifyId!=null">product_classify_id = #{productClassifyId,jdbcType=BIGINT},</if>
      <if test="productTitle!=null">product_title = #{productTitle,jdbcType=VARCHAR},</if>
      <if test="productDescribe!=null">product_describe = #{productDescribe,jdbcType=VARCHAR},</if>
      <if test="productService!=null">product_service = #{productService,jdbcType=VARCHAR},</if>
      <if test="isAlive!=null">is_alive = #{isAlive,jdbcType=INTEGER},</if>
      <if test="productOrderNum!=null">product_order_num = #{productOrderNum,jdbcType=INTEGER},</if>
      <if test="state!=null">`state` = #{state,jdbcType=INTEGER},</if>
      <if test="r1!=null">r_1 = #{r1,jdbcType=VARCHAR},</if>
      <if test="r2!=null">r_2 = #{r2,jdbcType=VARCHAR},</if>
      <if test="r3!=null">r_3 = #{r3,jdbcType=VARCHAR},</if>
      <if test="r4!=null">r_4 = #{r4,jdbcType=VARCHAR},</if>
      <if test="r5!=null">r_5 = #{r5,jdbcType=VARCHAR},</if>
      <if test="weight!=null">weight = #{weight,jdbcType=DECIMAL},</if>
      <if test="type!=null">type = #{type,jdbcType=INTEGER},</if>
      <if test="deliveryfee!=null">deliveryfee = #{deliveryfee,jdbcType=VARCHAR},</if>
      <if test="price!=null">price = #{price,jdbcType=DECIMAL},</if>
      <if test="sellprice!=null">sellprice = #{sellprice,jdbcType=DECIMAL},</if>
      <if test="spec!=null">spec = #{spec,jdbcType=VARCHAR},</if>
      <if test="sixnight!=null">sixnight = #{sixnight,jdbcType=VARCHAR},</if>
    </trim>
    where product_id = #{productId,jdbcType=BIGINT}
  </update>
  <sql id="Base_Column_List">
    product_id, create_time, update_time, store_id, product_classify_id, product_title, 
    product_service, is_alive, product_order_num, `state`, r_1, r_2, r_3, r_4, r_5, product_describe,weight,type,deliveryfee,price,sellprice,spec,sixnight
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from szm_c_product_new
    where product_id = #{productId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from szm_c_product_new order by create_time DESC
  </select>
    <select id="selectByClassId" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" />
    from szm_c_product_new where product_classify_id = #{classId} and product_title = #{productName}
    </select>
    <select id="selectByClasssAll" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" />
    from szm_c_product_new where product_classify_id = #{classId}
    </select>
  <select id="selectByName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from szm_c_product_new where product_title like concat(concat("%",#{name}),"%")
  </select>
  <select id="selectByClassName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from szm_c_product_new where product_title like concat(concat("%",#{name}),"%") and product_classify_id = #{classId}
  </select>
  <select id="selectProductAll" resultMap="BaseResultMap">
    select product_id,product_title
    from  szm_c_product_new
  </select>
  <select id="selectByProductName" resultMap="BaseResultMap">
    select product_id
    from szm_c_product_new
    where product_title = #{name}
  </select>
    <select id="selectProductCity" resultMap="BaseResultMap">
      select DISTINCT szm_c_product_new.r_5
      from szm_c_product_new
    </select>
  <select id="selectCityCodeList" resultMap="BaseResultMap">
    select product_id
    from szm_c_product_new
    where r_5 = #{cityCode}

  </select>
  <select id="selectProductList" resultMap="BaseResultMap">
    select a.product_id, a.create_time, a.update_time, a.store_id, a.product_classify_id, a.product_title,
    a.product_service, a.is_alive, a.product_order_num, a.`state`, a.r_1, a.r_2, a.r_3, a.r_4, a.r_5, a.product_describe,a.weight,a.type,a.deliveryfee,a.price,a.sellprice,a.spec,a.sixnight
    from szm_c_product_new as a inner join szm_c_product_model_new as b
    on a.product_id = b.product_id
    where EXISTS(select * from szm_c_product as b
    where a.product_title =b.product_title
    and b.store_id = #{storeId}
    )
    <if test="name!=null">
      and (a.product_title like concat(concat("%",#{name}),"%") or b.specifications_describe like concat(concat("%",#{name}),"%"))
    </if>
    <if test="classId!=null and 0 != classId">
      and a.product_classify_id = #{classId}
    </if>
    <if test="brandId!=null and 0 != brandId">
      and a.r_3 = #{brandId}
    </if>

  </select>


  <select id="selectProductListCs" resultMap="BaseResultMap">
    select 
    a.product_id, a.create_time, a.update_time, a.store_id, a.product_classify_id, a.product_title, 
    a.product_service, a.is_alive, a.product_order_num, a.`state`, a.r_1, a.r_2, a.r_3, a.r_4, a.r_5, a.product_describe,a.weight,a.type,a.deliveryfee,a.price,a.sellprice,a.spec,a.sixnight
    from szm_c_product_new as a inner join szm_c_product_model_new as b
    on a.product_id = b.product_id
where NOT EXISTS(select * from szm_c_product as b
        where a.product_title =b.product_title
        and b.store_id = #{storeId}
        )
    <if test="name!=null">
      and (a.product_title like concat(concat("%",#{name}),"%") or b.specifications_describe like concat(concat("%",#{name}),"%"))
    </if>
    <if test="classId!=null and 0 != classId">
      and a.product_classify_id = #{classId}
    </if>
    <if test="brandId!=null and 0 != brandId">
      and a.r_3 = #{brandId}
    </if>
  </select>
  <select id="selectProductListAll" resultMap="BaseResultMap">
    select 
    a.product_id, a.create_time, a.update_time, a.store_id, a.product_classify_id, a.product_title, 
    a.product_service, a.is_alive, a.product_order_num, a.`state`, a.r_1, a.r_2, a.r_3, a.r_4, a.r_5, a.product_describe,a.weight,a.type,a.deliveryfee,a.price,a.sellprice,a.spec,a.sixnight
    from szm_c_product_new as a inner join szm_c_product_model_new as b
    on a.product_id = b.product_id
    <where>
    <if test="name!=null">
      and (a.product_title like concat(concat("%",#{name}),"%") or b.specifications_describe like concat(concat("%",#{name}),"%"))
    </if>
    <if test="classId!=null and 0 != classId">
      and a.product_classify_id = #{classId}
    </if>
    <if test="brandId!=null and 0 != brandId">
      and a.r_3 = #{brandId}
    </if>
    </where>
  </select>
    <select id="selectProductCount" resultType="java.lang.Integer">
      select count(1)
      from szm_c_product_new
    where product_classify_id = #{classId} and product_id != #{productId} and product_title = #{productName}
    </select>
  <select id="selectProductTypeCount" resultType="java.lang.Integer">
    select count(1)
      from szm_c_product_new
    where product_title = #{productName}
  </select>

  <select id="selectOtherByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select product_id,is_alive
    from szm_c_product_new
    where product_id = #{productId,jdbcType=BIGINT}
  </select>

  <!-- updateBatchDeliveryFee --> 

  <update id="updateBatchDeliveryFee">
    update szm_c_product_new
    set deliveryfee = #{deliveryfee}
  </update>

  <!-- selectByBrandAndTitleLikeIn --> 

  <select id="selectByBrandAndTitleLikeIn" resultMap="BaseResultMap">
      select <include refid="Base_Column_List" />
    from szm_c_product_new where r_3 = #{brandid} and
        <foreach item="id" collection="specification" open="(" separator="and" close=")">
           product_title like concat(concat("%",#{id}),"%")
        </foreach>
  </select>

  <!-- 订单来源关联产品查询相关方法 -->

  <!-- 搜索产品 - 用于订单来源关联产品查询 -->
  <select id="searchProductsForOrderSource" resultType="java.util.Map">
    SELECT
      product_id as commodityId,
      product_title as commodityName,
      r_1 as img,
      COALESCE(sellprice, price, 0) as retail,
      COALESCE(price, 0) as cost,
      r_3 as brandId,
      product_classify_id as classId,
      product_id as skuId,
      '默认规格' as skuName,
      is_alive as storeState,
      100 as stock
    FROM szm_c_product_new
    <where>
      <if test="params.storeId != null">
        AND FIND_IN_SET(#{params.storeId}, store_id) > 0
      </if>
      <if test="params.keyword != null and params.keyword != ''">
        AND product_title LIKE #{params.keyword}
      </if>
      <if test="params.status != null">
        AND is_alive = #{params.status}
      </if>
    </where>
    ORDER BY create_time DESC
    <if test="params.pageNo != null and params.pageSize != null">
      LIMIT #{params.pageSize} OFFSET #{params.offset}
    </if>
  </select>

  <!-- 获取产品详情 - 用于订单来源关联产品查询 -->
  <select id="getProductDetailForOrderSource" resultType="java.util.Map">
    SELECT
      product_id as commodityId,
      product_title as commodityName,
      r_1 as img,
      COALESCE(sellprice, price, 0) as retail,
      COALESCE(price, 0) as cost,
      r_3 as brandId,
      product_classify_id as classId,
      product_id as skuId,
      '默认规格' as skuName,
      is_alive as storeState,
      100 as stock
    FROM szm_c_product_new
    WHERE product_id = #{productId}
      AND FIND_IN_SET(#{storeId}, store_id) > 0
    LIMIT 1
  </select>

  <!-- 获取产品总数 - 用于分页 -->
  <select id="countProductsForOrderSource" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM szm_c_product_new
    <where>
      <if test="params.storeId != null">
        AND FIND_IN_SET(#{params.storeId}, store_id) > 0
      </if>
      <if test="params.keyword != null and params.keyword != ''">
        AND product_title LIKE #{params.keyword}
      </if>
      <if test="params.status != null">
        AND is_alive = #{params.status}
      </if>
    </where>
  </select>

</mapper>