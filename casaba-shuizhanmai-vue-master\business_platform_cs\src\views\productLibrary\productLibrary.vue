<template>
  <div>
    <div class="headBox">
      <div class="flex algin-items-center padding-tb-10">
        <div class="optionBox">
          <el-select v-model="selectData.classId" @change="chooseClassify" :clearable="true" placeholder="按商品分类筛选"
            filterable>
            <el-option v-for="item in classifyOptions" :key="item.classifyId" :label="item.classifyName"
              :value="item.classifyId">
            </el-option>
          </el-select>
        </div>
        <div class="optionBox">
          <el-select v-model="selectData.brandId" :clearable="true" placeholder="按商品品牌筛选" filterable>
            <el-option v-for="(item, index) in bucketBrandList" :key="index" :label="item.brandname"
              :value="item.brandid">
            </el-option>
          </el-select>
        </div>
        <div class="optionBox">
          <el-select v-model="selectData.state" :clearable="true" placeholder="按商品上架状态筛选" filterable>
            <el-option v-for="item in upShelfList" :key="item.classifyId" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </div>
        <div class="margin-right-10">
          <el-input placeholder="请输入商品名称" v-model="selectData.isKey" clearable>
          </el-input>
        </div>
        <el-button type="primary" @click="toSearch">查询</el-button>
        <el-button style="margin-left:20px;" @click="clearSearch">清空筛选条件</el-button>
        <el-button type="primary" @click="toBrand">品牌管理</el-button>
        <el-button type="primary" @click="toClassify">分类管理</el-button>
        <el-button type="primary" @click="toAddGood">新增商品</el-button>
        <el-button type="primary" @click="batchUpShelf" :disabled="multipleSelection.length === 0">批量上架</el-button>
        <el-button type="warning" @click="showBatchDeliveryFeeDialog">一键设置配送费</el-button>
      </div>

    </div>
    <div class="tableBox margin-top-10">
      <el-table :data="list" :max-height="tableHeight + 70"
        :header-cell-style="{ 'text-align': 'center', 'background-color': '#EFF2F7' }"
        :cell-style="{ 'text-align': 'center', 'font-size': '13px', 'color': '#333C48' }" stripe border style="width: 100%;" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" :selectable="checkSelectable"></el-table-column>
        <!-- <el-table-column type="index" label="序号" width="80">
        </el-table-column> -->
        <el-table-column prop="" label="商品图" width="100">
          <template slot-scope="scope">
            <img :src="scope.row.img" alt="" style="width:75px;height:75px;">
          </template>
        </el-table-column>
        <el-table-column prop="commodityName" label="商品名称">
        </el-table-column>
        <el-table-column label="桶类型">
          <template slot-scope="scope">
            <template v-if="scope.row.lists != false">
              {{ scope.row.lists[0].buckState == "0" ? "常规桶" : scope.row.lists[0].buckState == "1" ? "一次性桶" : "-" }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="商品类型">
          <template slot-scope="scope">
            {{ (scope.row.type !== null && scope.row.type !== undefined && scope.row.type !== '') 
            ? productTypeList[scope.row.type].value : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="商品重量">
          <template slot-scope="scope">
            {{ scope.row.weight }}L
          </template>
        </el-table-column>
        <el-table-column label="配送费" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.deliveryfee" class="delivery-fee-table-cell" @click="update(scope.row)">
              <div v-for="(item, index) in parseDeliveryFee(scope.row.deliveryfee)" :key="index" class="delivery-fee-item">
                <span class="delivery-range">
                  <span v-if="index === 0">0件</span>
                  <span v-else>{{ parseDeliveryFee(scope.row.deliveryfee)[index-1].max }}件</span>
                  <span> - </span>
                  <span v-if="index !== parseDeliveryFee(scope.row.deliveryfee).length-1">{{ item.max }}件</span>
                  <span v-else>不限</span>
                </span>
                <span class="delivery-fee">{{ item.fee }}元</span>
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="零售价" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lists[0].retail" placeholder="请输入零售价"
              @input="getRetail($event, scope.row.lists[0].retail, scope.row.lists[0].cost, scope.$index)"
              :disabled="scope.row.storeState ? true : false"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="" label="成本价" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lists[0].cost" placeholder="请输入成本价"
              @input="getCost($event, scope.row.lists[0].retail, scope.row.lists[0].cost, scope.$index)"
              :disabled="scope.row.storeState ? true : false"></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column label="划线价" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lists[0].marketPrice"
              @input="getMarketPrice($event, scope.row.lists[0].retail, scope.row.lists[0].marketPrice, scope.$index)"
              placeholder="请输入划线价" :disabled="scope.row.storeState ? true : false"></el-input>
          </template>
        </el-table-column> -->
        <el-table-column prop="" label="毛利" width="100">
          <template slot-scope="scope">
            <span class="color-green"
              v-if="scope.row.lists[0].profit > 0">￥{{ scope.row.lists[0].profit ? scope.row.lists[0].profit : '0.00' }}</span>
            <span class="color-red"
              v-else-if="scope.row.lists[0].profit < 0">￥{{ scope.row.lists[0].profit ? scope.row.lists[0].profit : '0.00' }}</span>
            <span v-else>￥{{ scope.row.lists[0].profit ? scope.row.lists[0].profit : '0.00' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="实际库存" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lists[0].stock" placeholder="请输入实际库存"
              @input="getStock($event, scope.row.lists[0].stock, scope.$index)"
              :disabled="scope.row.storeState ? true : false"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="" label="虚拟库存" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.lists[0].virtualStock" oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="请输入虚拟库存" :disabled="scope.row.storeState ? true : false"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="update(scope.row)">修改</el-button>
            <el-button @click="upShelf(scope.row)" type="text" v-if="!scope.row.storeState">上架</el-button>
            <el-button :disabled="scope.row.storeState ? true : false" type="text" v-else>已上架</el-button>
            <!-- <el-button type='primary'
                       @click="setBucketMoney"
                       v-if="scope.row.className=='桶装水'">押桶管理</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="flex align-items-center justify-content-center">
        <el-pagination background layout="prev, pager, next" :total="pageCount" @current-change="clickPage"
          :current-page="page" style="padding:15px">
        </el-pagination>
      </div>

    </div>


    <!-- 新增商品对话框 -->
    <el-dialog title="新增商品" :visible.sync="addGoodDialog" width="50%">
      <el-form :model="addGoodForm" ref="addGoodForm" label-width="100px">
        <el-form-item label="商品分类" prop="classifyId" required>
          <el-select v-model="addGoodForm.classifyId" placeholder="请选择商品分类" style="width: 100%" @change="handleClassifyChange">
            <el-option v-for="item in classifyOptions" :key="item.classifyId" :label="item.classifyName"
              :value="item.classifyId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品牌分类" prop="brandId" required>
          <el-select v-model="addGoodForm.brandId" placeholder="请选择品牌" style="width: 100%" @change="handleBrandChange">
            <el-option v-for="item in bucketBrandList" :key="item.brandid" :label="item.brandname"
              :value="item.brandid"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品主图">
          <el-upload 
            action="https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload"
            ref="uploadMain"
            list-type="picture-card" 
            :file-list="mainImageList" 
            :on-preview="handlePictureCardPreview1"
            :on-remove="handleMainImgRemove" 
            :on-success="uploadMainImg" 
            :on-error="uploadError" 
            :on-exceed="uploadExceed"
            :before-upload="beforeUpload"
            :multiple="false" 
            accept=".jpg,.png,.jpeg,.gif" 
            :limit="1"> 
            <i class="el-icon-plus"></i> 
          </el-upload>
          <div><span class="font-size-12 color-grey">商品主图，用于商品列表展示(1张)</span></div>
        </el-form-item>

        <el-form-item label="商品轮播图">
          <el-upload
            action="https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload"
            ref="uploadCarousel"
            list-type="picture-card"
            :file-list="carouselImageList"
            :on-preview="handlePictureCardPreview1"
            :on-remove="handleCarouselImgRemove"
            :on-success="uploadCarouselImg"
            :on-error="uploadError"
            :on-exceed="uploadExceed"
            :before-upload="beforeUpload"
            :multiple="false"
            accept=".jpg,.png,.jpeg,.gif"
            :limit="1">
            <i class="el-icon-plus"></i>
          </el-upload>
          <div><span class="font-size-12 color-grey">商品详情页轮播图片(1张)</span></div>
        </el-form-item>

        <el-form-item label="商品详情图">
          <el-upload 
            action="https://szmsh.waterstation.com.cn/szm/uploadcontroller/upload"
            ref="uploadDetail"
            list-type="picture-card" 
            :file-list="detailImageList" 
            :on-preview="handlePictureCardPreview1"
            :on-remove="handleDetailImgRemove" 
            :on-success="uploadDetailImg" 
            :on-error="uploadError" 
            :on-exceed="uploadExceed"
            :before-upload="beforeUpload"
            :multiple="false" 
            accept=".jpg,.png,.jpeg,.gif" 
            :limit="1"> 
            <i class="el-icon-plus"></i> 
          </el-upload>
          <div><span class="font-size-12 color-grey">商品详情描述图片(1张)</span></div>
        </el-form-item>
        <el-form-item label="商品名称" prop="commodityName" required>
          <el-input v-model="addGoodForm.commodityName" placeholder="请输入商品名称" @input="handleTitleChange"></el-input>
        </el-form-item>
        <el-form-item label="69码" prop="sixnight">
          <el-input v-model="addGoodForm.sixnight" placeholder="请输入69码"></el-input>
        </el-form-item>
        <el-form-item label="商品规格" prop="details" >
          <el-input v-model="addGoodForm.details" placeholder="请输入商品规格" @input="handleSpecChange"></el-input>
        </el-form-item>
        <el-form-item label="商品重量" prop="weight" >
          <el-input v-model="addGoodForm.weight" placeholder="请输入商品重量" type="number">
            <template slot="append">L</template>
          </el-input>
        </el-form-item>
        <el-form-item label="商品类型" prop="type" >
          <el-select v-model="addGoodForm.type" placeholder="请选择商品类型" style="width: 100%">
            <el-option v-for="item in productTypeList" :key="item.key" :label="item.value" :value="item.key.toString()"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配送费设置">
          <div class="delivery-fee-steps">
            <div v-for="(item, index) in addGoodForm.deliveryfee" :key="index" style="margin-bottom: 10px; display: flex; align-items: center;">
              <span v-if="index === 0">0件</span>
              <span v-else>{{ addGoodForm.deliveryfee[index-1].max }}件</span>
              <span style="margin: 0 10px;">至</span>
              <el-input v-model="item.max" placeholder="件数" style="width: 150px;" v-if="index !== addGoodForm.deliveryfee.length-1">
                <template slot="append">件</template>
              </el-input>
              <span v-else>不限</span>
              <span style="margin: 0 10px;">收费</span>
              <el-input v-model="item.fee" placeholder="配送费" style="width: 150px;">
                <template slot="append">元</template>
              </el-input>
              <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeDeliveryFeeStep(index)" style="margin-left: 10px;" v-if="addGoodForm.deliveryfee.length > 1"></el-button>
            </div>
            <el-button type="primary" size="small" @click="addDeliveryFeeStep" style="margin-top: 10px;">
              <i class="el-icon-plus"></i> 添加阶梯
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="预估成本" prop="price" >
          <el-input v-model="addGoodForm.price" placeholder="请输入商品预估成本" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="预估零售价" prop="sellprice" >
          <el-input v-model="addGoodForm.sellprice" placeholder="请输入商品预估零售价" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="桶类型" prop="buckState">
          <el-select v-model="addGoodForm.buckState" placeholder="请选择桶类型" style="width: 100%">
            <el-option label="常规桶" value="0"></el-option>
            <el-option label="一次性桶" value="1"></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addGoodDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitAddGood">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 阶梯配送费详情对话框 -->
    <el-dialog title="阶梯配送费详情" :visible.sync="deliveryFeeDialogVisible" width="500px">
      <div class="delivery-fee-details">
        <div v-for="(item, index) in currentDeliveryFeeSteps" :key="index" class="delivery-fee-step">
          <div class="step-range">
            <span v-if="index === 0">0件</span>
            <span v-else>{{ currentDeliveryFeeSteps[index-1].max }}件</span>
            <span> - </span>
            <span v-if="index !== currentDeliveryFeeSteps.length-1">{{ item.max }}件</span>
            <span v-else>不限</span>
          </div>
          <div class="step-fee">{{ item.fee }}元</div>
        </div>
      </div>
    </el-dialog>

    <!-- 批量设置配送费对话框 -->
    <el-dialog title="批量设置配送费" :visible.sync="batchDeliveryFeeDialogVisible" width="600px">
      <div class="batch-delivery-fee">
        <el-alert
          title="注意：此操作将为所有商品设置统一的阶梯配送费，并覆盖之前所有的配送费设置！"
          type="warning"
          :closable="false"
          show-icon>
        </el-alert>
        <div class="delivery-fee-steps" style="margin-top: 20px;">
          <div v-for="(item, index) in batchDeliveryFeeSteps" :key="index" style="margin-bottom: 10px; display: flex; align-items: center;">
            <span v-if="index === 0">0件</span>
            <span v-else>{{ batchDeliveryFeeSteps[index-1].max }}件</span>
            <span style="margin: 0 10px;">至</span>
            <el-input v-model="item.max" placeholder="件数" style="width: 150px;" v-if="index !== batchDeliveryFeeSteps.length-1">
              <template slot="append">件</template>
            </el-input>
            <span v-else>不限</span>
            <span style="margin: 0 10px;">收费</span>
            <el-input v-model="item.fee" placeholder="配送费" style="width: 150px;">
              <template slot="append">元</template>
            </el-input>
            <el-button type="danger" icon="el-icon-delete" circle size="mini" @click="removeBatchDeliveryFeeStep(index)" style="margin-left: 10px;" v-if="batchDeliveryFeeSteps.length > 1"></el-button>
          </div>
          <el-button type="primary" size="small" @click="addBatchDeliveryFeeStep" style="margin-top: 10px;">
            <i class="el-icon-plus"></i> 添加阶梯
          </el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDeliveryFeeDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="applyBatchDeliveryFee">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 80px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.headBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.optionBox {
  margin-right: 10px;
}

.delivery-fee-details {
  padding: 10px;
}

.delivery-fee-step {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border-left: 4px solid #409EFF;
}

.step-range {
  font-weight: bold;
}

.step-fee {
  color: #f56c6c;
  font-weight: bold;
}

.delivery-fee-table-cell {
  text-align: left;
 // max-height: 100px;
  overflow-y: auto;
}

.delivery-fee-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: #f5f7fa;
  font-size: 12px;
}

.delivery-range {
  color: #606266;
}

.delivery-fee {
  color: #f56c6c;
  font-weight: bold;
}
</style>

<script>
import { productTypeList } from '@/data/product.js'

export default {
  props: {

  },
  data() {
    return {
      imgUri: this.$imgUri,
      // 上传配置
      // classId: 0, // 分类ID
      classifyOptions: [],
      bucketBrandList: [],
      upShelfList: [
        { name: "已上架", value: 1 },
        { name: "未上架", value: 2 }
      ],
      productTypeList: productTypeList,
      list: [],
      multipleSelection: [], // 多选数据
      selectData: {
        classId: "",
        brandId: "",
        state: "",
        isKey: ""
      },
      retail: "", // 零售价
      cost: "", // 成本价
      profit: "", // 毛利
      stock: "", // 实际库存,
      virtualStock: "", // 虚拟库存
      page: 1,
      pageCount: 0,
      // 押桶管理
      bucketDialog: false,
      bucketList: [],
      // 新增商品
      addGoodDialog: false, 
      specImgsModal1: false, 
      dialogImageUrl: "", 
      // 修改为与addGoods.js相同的数据结构
      productImgs: [], // 保留兼容性
      mainImage: "", // 商品主图 (对应数据库r_1字段)
      carouselImages: [], // 商品轮播图 (对应SKU的imgList字段)
      detailImage: "", // 商品详情图 (对应数据库r_4字段，单张图片)
      goodsClassifyName: "请选择分类",
      brandName: "请选择品牌",
      goodsTitle: "",
      goodsSpec: "",
      spuId: 0,
      skuId: 0,
      waterList: [],
      fileList: [], // 保留兼容性
      mainImageList: [], // 主图文件列表
      carouselImageList: [], // 轮播图文件列表
      detailImageList: [], // 详情图文件列表(单张)
      isSubmit: 0, // 防止重复提交
      addGoodForm: {
        classifyId: "",
        brandId: "",
        commodityName: "",
        details: "",
        productImgs: [], // 保留兼容性
        mainImage: "", // 商品主图
        carouselImages: [], // 商品轮播图
        detailImage: "", // 商品详情图(单张)
        weight: 0,
        type: 0,
        deliveryfee: [{max: null, fee: 0}], // 阶梯配送费，默认0-无限
        price: 0,
        sellprice: 0,
        sixnight: "", // 新增69码字段
      },
      deliveryFeeDialogVisible: false,
      currentDeliveryFeeSteps: [],
      batchDeliveryFeeDialogVisible: false, // 批量设置配送费对话框可见性
      batchDeliveryFeeSteps: [{max: null, fee: 0}], // 批量阶梯配送费设置
      // searchKey: ""
    }
  },
  computed: {
    tableHeight() {
      console.log(this.$store.getters.getGlobalHeight)
      let height = Number(this.$store.getters.getGlobalHeight) - 300
      if (height >= 300) {
        return height
      } else {
        return 300
      }
    },
    inTableHeight() {
      let height = this.$store.getters.getGlobalHeight
      if (height >= 400) {
        return parseInt(this.$util.mul(height, 0.5))
      } else {
        return 400
      }
    }
  },
  created() {

  },
  mounted() {
    let that = this
    that.load()
    that.lookUpBrand()
  },
  watch: {

  },
  methods: {
    toBrand() {
      this.$router.push({
        path: "/brandProductLibrary"
      })
    },
    toClassify() {
      this.$router.push({
        path: "/classifyProductLibrary"
      })
    },
    load() {
      let that = this
      let isurl = "/szmb/newinsertproductcontroller/selectstoreproductpc"

      // 处理多关键词搜索：将空格分隔的关键词转换为数组
      let searchKeywords = that.selectData.isKey ?
        that.selectData.isKey.trim().split(/\s+/).filter(keyword => keyword.length > 0) : []

      let o = {
        storeId: that.Cookies.get("storeId"),
        classId: that.selectData.classId ? that.selectData.classId : 0,
        brandId: that.selectData.brandId,
        index: that.page,
        productName: that.selectData.isKey, // 保持原有参数兼容性
        searchKeywords: searchKeywords.length > 1 ? JSON.stringify(searchKeywords) : '', // 只有多个关键词时才传递
        isMultiKeywordSearch: searchKeywords.length > 1 ? 1 : 0, // 标识是否为多关键词搜索
        state: that.selectData.state ? that.selectData.state : 0
      }

      // 调试信息：打印搜索参数
      if (searchKeywords.length > 1) {
        console.log('多关键词搜索参数:', {
          原始搜索文本: that.selectData.isKey,
          解析后关键词: searchKeywords,
          传递给后端的参数: o
        })
      }
      that.$post(isurl, o).then(res => {
        that.classifyLoad()
        if (res.code == 1) {
          // that.classifyOptions = res.data.shopClasses
          // that.brandOptions = res.data.brands
          let list = res.data.shopContents
          list.forEach(function (item) {
            let cost = item.lists[0].cost
            let retail = item.lists[0].retail
            item.lists[0].profit = that.$util.sub(retail, cost)

            // 调试：打印商品详情图数据
            if (item.detailImages && item.detailImages.length > 0) {
              console.log('商品详情图数据:', item.commodityName, item.detailImages)
            }
          })
          that.list = list
          that.pageCount = res.data.pageCount
        }
      })
    },
    classifyLoad() {
      let that = this
      let isurl = "/szmb/newclasscontroller/selectclass"
      that.$post(isurl, {}).then(res => {
        if (res.code == 1) {
          // let json = {
          //   classifyId: 0,
          //   classifyName: "全部"
          // }
          // res.data.unshift(json)
          // console.log(arr, "arr")
          that.classifyOptions = res.data
        } else {
          this.$alert(res.data, "温馨提示", {
            confirmButtonText: "确定",
            callback: action => {
            }
          })
        }
      })
    },
    lookUpBrand() {
      let that = this
      let isurl = "/szmb/newclasscontroller/selectbrand"
      let o = {
        // storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then(res => {
        if (res.code == 1) {
          that.bucketBrandList = res.data
        } else {
          that.$message.error(res.data)
        }
      })
    },
    clickPage(e) {
      let that = this
      that.page = e
      that.load()
    },
    chooseClassify(e) {
      let that = this
      if (!e) {
        that.selectData.classId = ""
      }
      that.selectData.isKey = ""
      // that.page = 1
      // that.load()
    },
    // 清除搜索关键字
    clearSearch() {
      let that = this
      that.page = 1
      that.selectData = {
        classId: "",
        brandId: "",
        state: "",
        isKey: ""
      }
      that.load()
    },
    // 产品库搜索
    toSearch() {
      let that = this
      that.page = 1
      that.load()
    },
    getRetail(e, a, b, c) {
      let that = this
      e = e.replace(/[^\d.]/g, "")
      that.list[c].lists[0].retail = e
      if (!isNaN(a) && !isNaN(b)) {
        that.$set(that.list[c].lists[0], "profit", that.$util.sub(a, b).toFixed(2))
      }
    },
    getCost(e, a, b, c) {
      let that = this
      e = e.replace(/[^\d.]/g, "")
      that.list[c].lists[0].cost = e
      if (!isNaN(a) && !isNaN(b)) {
        that.$set(that.list[c].lists[0], "profit", that.$util.sub(a, b).toFixed(2))
      }
    },
    getMarketPrice(e, a, b, c) {
      let that = this
      e = e.replace(/[^\d.]/g, "")
      that.list[c].lists[0].marketPrice = e
    },
    getStock(e, a, b) {
      let that = this
      e = e.replace(/[^\d]/g, "")
      that.list[b].lists[0].stock = e
      if (!isNaN(a)) {
        that.$set(that.list[b].lists[0], "virtualStock", a * 2)
      }
    },
    upShelf(e) {
      console.log(e)
      let that = this
      let data = e
      if (!Number(data.lists[0].retail)) {
        console.log(Number(data.lists[0].profit.retail))
        that.$message.error("请输入有效的零售价")
        return
      }
      if (!Number(data.lists[0].cost)) {
        that.$message.error("请输入有效的成本价")
        return
      }

      if (!Number(data.lists[0].stock)) {
        that.$message.error("请输入有效的库存数量")
        return
      }
      let arr = [{
        storeId: that.Cookies.get("storeId"),
        id: data.classId,
        classNmae: data.className,
        brandId: data.brandId,
        brandName: data.brandName,
        commodityName: data.commodityName,
        source: 1,
        spuId: 0,
        type: 0,
        productDescribe: data.productDescribe,
        weight: data.weight,
        type: data.type,
        deliveryfee: data.deliveryfee,
        price: data.price,
        sellprice: data.sellprice,
        lists: [{
          imgList: data.lists[0].imgList,
          skuId: 0,
          cost: data.lists[0].cost,
          marketPrice: data.lists[0].marketPrice,
          retail: data.lists[0].retail,
          stock: data.lists[0].stock,
          virtualStock: data.lists[0].virtualStock,
          source: 0,
          details: data.lists[0].skuName,
          isWater: 0,
          integralChecked: 1,
          // add by yxw 2020-3-3 产品库添加桶类型
          buckState: data.lists[0].buckState
        }]
      }]
      let isurl = "/szmb/newinsertproductcontroller/insertproductlist"
      let o = {
        list: arr,
        header: "json"
      }
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "上架成功！",
            type: "success"
          })
          that.load()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 押桶管理
    setBucketMoney() {
      let that = this
      let isurl = "/szmb/szmbuserandstorebillcontroller/selectstorebuck"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then(res => {
        if (res.code == 1) {
          that.bucketList = res.data
          that.bucketDialog = true
        } else {
          that.$message.error(res.data)
        }
      })
    },
    bucketSetSubmit() {
      let that = this
      let list = that.bucketList
      let arr = list.map(function (item) {
        let json = {
          brandId: item.brandId,
          money: item.money ? item.money : 0
        }
        return json
      })
      let isurl = "/szmb/szmbuserandstorebillcontroller/insertstorebuck"
      let o = {
        storeId: that.Cookies.get("storeId"),
        brandList: JSON.stringify(arr)
      }
      that.$post(isurl, o).then(res => {
        if (res.code == 1) {
          that.$message({
            message: "设置成功!",
            type: "success"
          })
          that.bucketDialog = false
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 表单字段处理方法
    handleClassifyChange(val) {
      let that = this
      let classObj = that.classifyOptions.find(item => item.classifyId === val)
      that.goodsClassifyName = classObj ? classObj.classifyName : '请选择分类'
    },
    
    handleBrandChange(val) {
      let that = this
      let brandObj = that.bucketBrandList.find(item => item.brandid === val)
      that.brandName = brandObj ? brandObj.brandname : '请选择品牌'
    },
    
    handleTitleChange(val) {
      this.goodsTitle = val
    },
    
    handleSpecChange(val) {
      this.goodsSpec = val
    },

    // 新增商品相关方法
    toAddGood() {
      let that = this
      that.addGoodDialog = true
      // 重置数据
      that.productImgs = []
      that.mainImage = ""
      that.carouselImages = []
      that.detailImage = ""
      that.goodsClassifyName = "请选择分类"
      that.brandName = "请选择品牌"
      that.goodsTitle = ""
      that.goodsSpec = ""
      that.spuId = 0
      that.skuId = 0
      that.waterList = []
      that.fileList = []
      that.mainImageList = []
      that.carouselImageList = []
      that.detailImageList = []

      // 保持原有的表单对象，用于兼容vue中的表单绑定
      that.addGoodForm = {
        classifyId: "",
        brandId: "",
        commodityName: "",
        details: "",
        productImgs: [],
        mainImage: "",
        carouselImages: [],
        detailImage: "",
        weight: 0,
        type: 0,
        deliveryfee: [{max: null, fee: 0}], // 阶梯配送费，默认0-无限
        price: 0,
        sellprice: 0,
        sixnight: "", // 新增69码字段
        buckState: "0"
      }

      that.$nextTick(function () {
        // 清空所有上传组件
        if (that.$refs.uploadMain) that.$refs.uploadMain.clearFiles()
        if (that.$refs.uploadCarousel) that.$refs.uploadCarousel.clearFiles()
        if (that.$refs.uploadDetail) that.$refs.uploadDetail.clearFiles()
        // 保持兼容性
        if (that.$refs.upload1) that.$refs.upload1.clearFiles()
      })
    },

    handlePictureCardPreview1(file) {
      this.dialogImageUrl = file.url
      this.specImgsModal1 = true
    },

    // 上传前验证
    beforeUpload(file) {
      const isImage = /^image\//.test(file.type)
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    // 主图上传成功
    uploadMainImg(response, file, fileList) {
      console.log('主图上传:', response, fileList)
      let that = this
      if (response.code == 1) {
        that.mainImageList = fileList
        that.mainImage = ("https://waterstation.com.cn/szm" + response.data)
        that.addGoodForm.mainImage = ("https://waterstation.com.cn/szm" + response.data)
        console.log(that.addGoodForm.mainImage )
        that.$message.success('主图上传成功')
      } else {
        that.$message.error(response.data || '主图上传失败')
        // 上传失败时移除失败的文件
        that.mainImageList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 主图删除
    handleMainImgRemove(file, fileList) {
      console.log('主图删除:', file, fileList)
      let that = this
      that.mainImageList = fileList
      // 删除时清空主图数据，但不覆盖已有的有效数据
      if (fileList.length === 0) {
        that.mainImage = ""
        that.addGoodForm.mainImage = ""
      }
    },

    // 轮播图上传成功
    uploadCarouselImg(response, file, fileList) {
      console.log('轮播图上传:', response, fileList)
      let that = this
      if (response.code == 1) {
        that.carouselImageList = fileList
        // 单张轮播图处理
        that.carouselImages = ["https://waterstation.com.cn/szm" + response.data]
        that.addGoodForm.carouselImages = ["https://waterstation.com.cn/szm" + response.data]
        // 保持兼容性
        that.productImgs = ["https://waterstation.com.cn/szm" + response.data]
        that.addGoodForm.productImgs = ["https://waterstation.com.cn/szm" + response.data]
        that.$message.success('轮播图上传成功')
      } else {
        that.$message.error(response.data || '轮播图上传失败')
        // 上传失败时移除失败的文件
        that.carouselImageList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 轮播图删除
    handleCarouselImgRemove(file, fileList) {
      console.log('轮播图删除:', file, fileList)
      let that = this
      that.carouselImageList = fileList
      // 删除时清空轮播图数据
      if (fileList.length === 0) {
        that.carouselImages = []
        that.addGoodForm.carouselImages = []
        // 保持兼容性
        that.productImgs = []
        that.addGoodForm.productImgs = []
      }
    },

    // 详情图上传成功
    uploadDetailImg(response, file, fileList) {
      console.log('详情图上传:', response, fileList)
      let that = this
      if (response.code == 1) {
        that.detailImageList = fileList
        that.detailImage = ("https://waterstation.com.cn/szm" + response.data)
        that.addGoodForm.detailImage = ("https://waterstation.com.cn/szm" + response.data)
        that.$message.success('详情图上传成功')
      } else {
        that.$message.error(response.data || '详情图上传失败')
        // 上传失败时移除失败的文件
        that.detailImageList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    // 详情图删除
    handleDetailImgRemove(file, fileList) {
      console.log('详情图删除:', file, fileList)
      let that = this
      that.detailImageList = fileList
      // 删除时清空详情图数据，但不覆盖已有的有效数据
      if (fileList.length === 0) {
        that.detailImage = ""
        that.addGoodForm.detailImage = ""
      }
    },

    // 保留原有方法以兼容性
    uploadImg1(response, file, fileList) {
      // 重定向到轮播图上传
      this.uploadCarouselImg(response, file, fileList)
    },

    handleImgRemove(file, fileList) {
      // 重定向到轮播图删除
      this.handleCarouselImgRemove(file, fileList)
    },

    uploadError() {
      let that = this
      that.$message.error("图片上传失败!")
    },

    uploadExceed() {
      let that = this
      that.$message.error("图片数量已超上限!")
    },

    submitAddGood() {
      let that = this

      // 防止重复提交
      if (that.isSubmit == 1) {
        that.$message.error("操作过于频繁，请稍后再试~")
        return
      }
      that.isSubmit = 1
      setTimeout(function () {
        that.isSubmit = 0
      }, 2000)

      // 将表单数据同步到addGoods.js中的数据结构
      let classifyId = that.addGoodForm.classifyId
      let brandId = that.addGoodForm.brandId
      let commodityName = that.addGoodForm.commodityName
      let details = that.addGoodForm.details
      // 使用新的图片字段结构
      let mainImage = that.addGoodForm.mainImage || ""
      let carouselImages = that.addGoodForm.carouselImages || []
      let detailImage = that.addGoodForm.detailImage || ""
      // 保持兼容性，如果没有轮播图则使用主图
      let imgList = carouselImages.length > 0 ? carouselImages : (mainImage ? [mainImage] : [])
      let weight = that.addGoodForm.weight
      let type = that.addGoodForm.type
      let deliveryFeeSteps = that.addGoodForm.deliveryfee
      let price = that.addGoodForm.price
      let sellprice = that.addGoodForm.sellprice
      
      if (!classifyId) {
        that.$message.error('请选择商品分类')
        that.isSubmit = 0
        return
      }
      if (!brandId) {
        that.$message.error('请选择品牌分类')
        that.isSubmit = 0
        return
      }
      // 图片不是必须的
      // if (imgList.length === 0) {
      //   that.$message.error('请上传商品图片')
      //   return
      // }
      if (!commodityName) {
        that.$message.error('请输入商品名称')
        that.isSubmit = 0
        return
      }
      if (!details) {
        that.$message.error('请输入商品规格')
        that.isSubmit = 0
        return
      }
      // if (!weight) {
      //   that.$message.error('请输入商品重量')
      //   that.isSubmit = 0
      //   return
      // }
      // if (!type && type !== 0) {
      //   that.$message.error('请选择商品类型')
      //   that.isSubmit = 0
      //   return
      // }
      
      // 验证配送费数据
      if (!deliveryFeeSteps || deliveryFeeSteps.length === 0) {
        that.$message.error('请设置阶梯配送费')
        that.isSubmit = 0
        return
      }
      
      // 验证阶梯区间的合理性
      for (let i = 0; i < deliveryFeeSteps.length; i++) {
        if (i < deliveryFeeSteps.length - 1) {
          if (!deliveryFeeSteps[i].max) {
            that.$message.error(`请输入第${i+1}个配送区间的上限件数`)
            that.isSubmit = 0
            return
          }
          
          if (i > 0 && Number(deliveryFeeSteps[i].max) <= Number(deliveryFeeSteps[i-1].max)) {
            that.$message.error(`第${i+1}个配送区间的上限必须大于前一个区间的上限`)
            that.isSubmit = 0
            return
          }
        }
        
        if (deliveryFeeSteps[i].fee === undefined || deliveryFeeSteps[i].fee === null || deliveryFeeSteps[i].fee === '') {
          that.$message.error(`请输入第${i+1}个配送区间的配送费`)
          that.isSubmit = 0
          return
        }
      }
      
      // if (!price) {
      //   that.$message.error('请输入商品价格')
      //   that.isSubmit = 0
      //   return
      // }
      // if (!sellprice) {
      //   that.$message.error('请输入商品销售价格')
      //   that.isSubmit = 0
      //   return
      // }

      // 获取分类名称和品牌名称
      let classObj = that.classifyOptions.find(item => item.classifyId === classifyId)
      let brandObj = that.bucketBrandList.find(item => item.brandid === brandId)

      let className = classObj ? classObj.classifyName : ''
      let brandName = brandObj ? brandObj.brandname : ''
      
      // 按照addGoods.js中的数据结构构造提交数据
      let o = {
        commodityName,
        lists: [{
          details,
          imgList, // SKU级别的轮播图
          skuId: that.skuId
        }],
        id: classifyId,
        brandId,
        spuId: that.spuId,
        brandName: brandName,
        className: className,
        waterList: JSON.stringify(that.waterList || []),
        weight: weight,
        productNewType: type,
        deliveryfee: JSON.stringify(deliveryFeeSteps),
        price: price,
        sellprice: sellprice,
        spec: details,
        sixnight: that.addGoodForm.sixnight, // 添加69码字段
        // 新增图片字段
        mainImage: mainImage, // SPU级别的主图，将存储到r_1字段
        detailImage: detailImage, // SPU级别的详情图，将存储到r_4字段(单张图片)
        header: "json"
      }

      let isurl = "/szmb/newinsertproductcontroller/insertproduct"
      
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "新增商品成功！",
            type: "success"
          })
          that.addGoodDialog = false
          that.load() // 刷新列表
        } else {
          that.$message.error(res.data)
        }
        that.isSubmit = 0
      }).catch(function(error) {
        that.isSubmit = 0
        that.$message.error("提交失败，请稍后重试")
      })
    },

    // 新增编辑商品方法
    update(row) {
      let that = this
      that.addGoodDialog = true
      that.spuId = row.commodityId || 0

      // 处理图片字段
      // 1. 主图 (来自SPU的r_1字段，通常是img字段)
      that.mainImage = row.img || ""
      that.addGoodForm.mainImage = that.mainImage
      that.mainImageList = that.mainImage ? [{
        name: that.mainImage.split('/').pop(),
        url: that.mainImage
      }] : []

      // 2. 轮播图 (来自SKU的imgList字段，只取第一张)
      if (row.lists && row.lists.length > 0) {
        that.skuId = row.lists[0].skuId || 0
        const imgList = row.lists[0].imgList || []
        const firstImage = imgList.length > 0 ? imgList[0] : ""
        that.carouselImages = firstImage ? [firstImage] : []
        that.addGoodForm.carouselImages = that.carouselImages
        that.carouselImageList = firstImage ? [{
          name: firstImage.split('/').pop(),
          url: firstImage
        }] : []
        // 保持兼容性
        that.productImgs = that.carouselImages
        that.addGoodForm.productImgs = that.carouselImages
        that.fileList = that.carouselImageList

        that.addGoodForm.details = row.lists[0].skuName || ''
        that.addGoodForm.skuId = row.lists[0].skuId || ''
      }

      // 3. 详情图 (来自SPU的detailsUrl字段，支持多张图片)
      that.detailImage = row.detailsUrl || row.detailImage || row.r4 || ""
      that.addGoodForm.detailImage = that.detailImage

      // 如果有detailImages数组，使用第一张作为详情图
      if (row.detailImages && row.detailImages.length > 0) {
        that.detailImage = row.detailImages[0]
        that.addGoodForm.detailImage = that.detailImage
      }

      that.detailImageList = that.detailImage ? [{
        name: that.detailImage.split('/').pop(),
        url: that.detailImage
      }] : []

      that.goodsClassifyName = row.className || '请选择分类'
      that.brandName = row.brandName || '请选择品牌'
      that.goodsTitle = row.commodityName || ''
      that.goodsSpec = that.addGoodForm.details
      that.addGoodForm.classifyId = row.classId || ''
      that.addGoodForm.brandId = row.brandId || ''
      that.addGoodForm.commodityName = row.commodityName || ''
      that.addGoodForm.weight = row.weight || ''
      that.addGoodForm.type = (row.type !== undefined && row.type !== null && row.type !== '' ) ? row.type.toString() : ''
      that.addGoodForm.sixnight = row.sixnight || '' // 添加69码字段的加载

      // 处理配送费数据
      that.addGoodForm.deliveryfee = row.deliveryfee ? JSON.parse(row.deliveryfee) : [{max: null, fee: 0}]
      console.log(that.addGoodForm.deliveryfee)
      that.addGoodForm.price = row.price || ''
      that.addGoodForm.sellprice = row.sellprice || ''

      // 获取水厂列表
      // that.getWaterList()
    },
    
    getWaterList() {
      // 这里可以添加获取水厂列表的方法
      // 根据小程序的实现，如果需要获取水厂列表的话
      let that = this
      if (that.spuId) {
        // 可以根据spuId获取关联的水厂列表
      }
    },

    addDeliveryFeeStep() {
      let that = this
      that.addGoodForm.deliveryfee.push({ max: null, fee: 0 })
    },

    removeDeliveryFeeStep(index) {
      let that = this
      that.addGoodForm.deliveryfee.splice(index, 1)
    },

    showDeliveryFeeDetails(row) {
      this.currentDeliveryFeeSteps = row.deliveryfee ? JSON.parse(row.deliveryfee) : [{max: null, fee: 0}]
      this.deliveryFeeDialogVisible = true
    },

    // 显示批量设置配送费对话框
    showBatchDeliveryFeeDialog() {
      this.batchDeliveryFeeSteps = [{max: null, fee: 0}]
      this.batchDeliveryFeeDialogVisible = true
    },

    // 添加阶梯配送费步骤
    addBatchDeliveryFeeStep() {
      let that = this
      if (that.batchDeliveryFeeSteps.length > 0) {
        const lastItem = that.batchDeliveryFeeSteps[that.batchDeliveryFeeSteps.length - 1]
        if (lastItem.max === null) {
          // 替换最后一个无限项为有限项
          that.batchDeliveryFeeSteps.splice(that.batchDeliveryFeeSteps.length - 1, 1, {
            max: "",
            fee: lastItem.fee
          })
        }
      }
      // 添加新的无限项
      that.batchDeliveryFeeSteps.push({max: null, fee: 0})
    },

    // 删除阶梯配送费步骤
    removeBatchDeliveryFeeStep(index) {
      let that = this
      that.batchDeliveryFeeSteps.splice(index, 1)
      
      // 确保最后一项总是无限项
      if (that.batchDeliveryFeeSteps.length > 0) {
        const lastItem = that.batchDeliveryFeeSteps[that.batchDeliveryFeeSteps.length - 1]
        if (lastItem.max !== null) {
          lastItem.max = null
        }
      }
    },

    // 应用批量配送费
    applyBatchDeliveryFee() {
      let that = this
      // 验证输入
      for (let i = 0; i < that.batchDeliveryFeeSteps.length; i++) {
        if (i < that.batchDeliveryFeeSteps.length - 1) {
          if (!that.batchDeliveryFeeSteps[i].max) {
            that.$message.error(`请输入第${i+1}个配送区间的上限件数`)
            return
          }
          
          if (i > 0 && Number(that.batchDeliveryFeeSteps[i].max) <= Number(that.batchDeliveryFeeSteps[i-1].max)) {
            that.$message.error(`第${i+1}个配送区间的上限必须大于前一个区间的上限`)
            return
          }
        }
        
        if (that.batchDeliveryFeeSteps[i].fee === undefined || that.batchDeliveryFeeSteps[i].fee === null || that.batchDeliveryFeeSteps[i].fee === '') {
          that.$message.error(`请输入第${i+1}个配送区间的配送费`)
          return
        }
        
        // 转换为数字
        that.batchDeliveryFeeSteps[i].fee = Number(that.batchDeliveryFeeSteps[i].fee)
        if (i < that.batchDeliveryFeeSteps.length - 1) {
          that.batchDeliveryFeeSteps[i].max = Number(that.batchDeliveryFeeSteps[i].max)
        }
      }

      // 准备配送费数据
      const deliveryFeeStr = JSON.stringify(that.batchDeliveryFeeSteps)

      // 准备请求参数
      let params = {
        deliveryfee: deliveryFeeStr,
        header: "json"
      }

      // 显示确认对话框
      that.$confirm(`确认为所有商品设置阶梯配送费吗？此操作将覆盖所有商品的配送费设置。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 发送请求到后端
        let isurl = "/szmb/newinsertproductcontroller/updateBatchDeliveryFee"
        
        // 显示加载中提示
        const loading = that.$loading({
          lock: true,
          text: '正在更新配送费...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        that.$get(isurl, {deliveryfee: deliveryFeeStr}).then(res => {
          loading.close()
          if (res.code == 1) {
            that.$message({
              message: "批量设置配送费成功！",
              type: "success"
            })
            that.load() // 重新加载数据
          } else {
            that.$message.error(res.data || "操作失败")
          }
        }).catch(error => {
          loading.close()
          that.$message.error("操作失败，请稍后重试")
          console.error(error)
        })
        
        that.batchDeliveryFeeDialogVisible = false
      }).catch(() => {
        // 取消操作
      })
    },

    // 多选表格变化
    handleSelectionChange(val) {
      this.multipleSelection = val.filter(item => !item.storeState);
    },
    
    // 批量上架
    batchUpShelf() {
      let that = this;
      if (that.multipleSelection.length === 0) {
        that.$message.error("请选择需要上架的商品");
        return;
      }
      
      // 验证所有选中的商品是否都有必要的数据
      let invalidItems = that.multipleSelection.filter(item => {
        return !Number(item.lists[0].retail) || 
               !Number(item.lists[0].cost) || 
               !Number(item.lists[0].stock);
      });
      
      if (invalidItems.length > 0) {
        that.$message.error("选中的商品中有未设置零售价、成本价或库存的商品，请检查");
        return;
      }
      
      // 构建批量上架数据
      let arr = that.multipleSelection.map(data => {
        return {
          storeId: that.Cookies.get("storeId"),
          id: data.classId,
          classNmae: data.className,
          brandId: data.brandId,
          brandName: data.brandName,
          commodityName: data.commodityName,
          source: 1,
          spuId: 0,
          type: 0,
          productDescribe: data.productDescribe,
          weight: data.weight,
          type: data.type,
          deliveryfee: data.deliveryfee,
          price: data.price,
          sellprice: data.sellprice,
          lists: [{
            imgList: data.lists[0].imgList,
            skuId: 0,
            cost: data.lists[0].cost,
            marketPrice: data.lists[0].marketPrice,
            retail: data.lists[0].retail,
            stock: data.lists[0].stock,
            virtualStock: data.lists[0].virtualStock,
            source: 0,
            details: data.lists[0].skuName,
            isWater: 0,
            integralChecked: 1,
            buckState: data.lists[0].buckState
          }]
        };
      });
      
      let isurl = "/szmb/newinsertproductcontroller/insertproductlist";
      let o = {
        list: arr,
        header: "json"
      };
      
      that.$confirm('确认批量上架选中的' + arr.length + '件商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$post(isurl, o).then(function (res) {
          if (res.code == 1) {
            that.$message({
              message: "批量上架成功！",
              type: "success"
            });
            that.multipleSelection = [];
            that.load();
          } else {
            that.$message.error(res.data);
          }
        });
      }).catch(() => {
        // 取消操作
      });
    },
    checkSelectable(row) {
      return !row.storeState;
    },
    parseDeliveryFee(deliveryFeeString) {
      try {
        return JSON.parse(deliveryFeeString) || [{max: null, fee: 0}];
      } catch (e) {
        return [{max: null, fee: 0}];
      }
    },
  },
  components: {

  }
}
</script>

<style lang="scss" scoped>
.headBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.optionBox {
  margin-right: 10px;
}
</style>
