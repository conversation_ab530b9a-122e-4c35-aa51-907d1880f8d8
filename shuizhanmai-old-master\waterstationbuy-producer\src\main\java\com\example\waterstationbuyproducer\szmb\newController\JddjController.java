package com.example.waterstationbuyproducer.szmb.newController;

import cn.hutool.core.lang.Validator;
import com.example.waterstationbuyproducer.dao.*;
import com.example.waterstationbuyproducer.entity.*;
import com.example.waterstationbuyproducer.jd.RequestUtil;
import com.example.waterstationbuyproducer.szmb.service.hz.order.SzmBOrderService;
import com.example.waterstationbuyproducer.szmb.service.order.OrderSourceService;
import com.example.waterstationbuyproducer.szmb.vo.JddjKeyVo;
import com.example.waterstationbuyproducer.szmc.service.SzmCUserService;
import com.example.waterstationbuyproducer.util.*;
import com.example.waterstationbuyproducer.util.logger.LoggerUtil;
import com.example.waterstationbuyproducer.util.sms.RemindSMS;
import com.example.waterstationbuyproducer.util.sms.UtilSMS;
import com.example.waterstationbuyproducer.wx.impl.WeiXinCommonServiceImpl;
import com.gexin.fastjson.JSON;
import com.gexin.fastjson.JSONArray;
import com.gexin.fastjson.JSONObject;
import com.taobao.api.ApiException;
import o2o.openplatform.sdk.dto.WebRequestDTO;
import o2o.openplatform.sdk.util.HttpUtil;
import o2o.openplatform.sdk.util.SignUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @describe:
 * @program: shuizhanmai-old-master
 * @author: cjy
 * @create: 2024-09-11 22:32
 */
@RestController
@RequestMapping("jddj")
public class JddjController {
    
    private static final int pageSize = 2;
    private static final Logger logger = LoggerFactory.getLogger(JddjController.class);

    public static Map<String,JddjKeyVo> jddjKeyVoMap = JddjKeyVo.init();

    String token = "b3ab79e7-b263-40f9-bc30-d6dda3f88c85";
    String appKey = "603690dcdf08490d9f134c29fe224248";
    String appSecret = "571200344d21433c8919dcbb34e26dbe";


    String token1 = "cb4b27ae-6b62-42d3-bfa6-6ccd07a7e01b";
    String appKey1 = "5c0f6c36b013486b8200ca0fa4a121e8";
    String appSecret1 = "087f1e90b1a048718960608ba39525d0";

    String token2 = "d9ec37f0-1afd-4a75-8984-3b6d853e54a2";
    String appKey2 = "d794a292fc884a568a800d47ddaa5e01";
    String appSecret2 = "2743b7b07e5846c299e81ab44d162015";

    String token3 = "2b133c68-8436-4d03-ba75-af3787cc7405";
    String appKey3 = "06b1de86a7364508b3cc5d2e17bc6170";
    String appSecret3 = "fad8d6175f534e7382bc361d56b89163";

    @Autowired
    private SzmCUserMapper szmCUserMapper;
    @Autowired
    private SzmCUserService szmCUserService;
    @Autowired
    private SzmCAddressMapper szmCAddressMapper;
    @Autowired
    private SzmCOrderMainMapper szmCOrderMainMapper;
    @Autowired
    private SmzCOrderDetailsMapper smzCOrderDetailsMapper;
    @Autowired
    private SzmBOrderService szmBOrderService;
    
    @Autowired
    private OrderSourceService orderSourceService;


    @Autowired
    private StoreSmsInfoMapper storeSmsInfoMapper;

    @Autowired
    private SmsRelevanceMapper smsRelevanceMapper;

    @Autowired
    private SmsMasterMapper smsMasterMapper;

    @Autowired
    private SmsRecordMapper smsRecordMapper;

    @Autowired
    private StoreMsgMapper storeMsgMapper;

    @Autowired
    private SzmCUserinfoMapper szmCUserinfoMapper;

    @Autowired
    private SzmCStoreApplyForMapper szmCStoreApplyForMapper;
    @Autowired
    private SmzCOrderReturnsMapper smzCOrderReturnsMapper;
    @Autowired
    private TaobaoController taobaoController;
    @Autowired
    private ElementOrderController elementOrderController;
    @Autowired
    private StoreIdUtil storeIdUtil;

    @Autowired
    private SzmCProductNewMapper szmCProductNewMapper;

    @Autowired
    private OrderSourceConnectMapper orderSourceConnectMapper;


    @RequestMapping("refreshPhone")
    public ResultBean refreshPhone(Long orderId) throws ApiException {
        ResultBean resultBean = new ResultBean();

        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByPrimaryKey(orderId);
        if (null == szmCOrderMain) {
            return new ResultBean().error();
        }
        if (szmCOrderMain.getOrdersource() != null && szmCOrderMain.getOrdersource().equals(7)) {
            // 淘宝订单
           return taobaoController.desensitizephone(szmCOrderMain.getOrderNum());
        }
        if (szmCOrderMain.getOrdersource() != null && szmCOrderMain.getOrdersource().equals(2)) {
            // 饿了么订单
            return elementOrderController.refreshPhone(szmCOrderMain.getOrderNum());
        }
        if (szmCOrderMain.getOrdersource() != null && szmCOrderMain.getOrdersource().equals(1)) {
            // 京东到家订单
        }
        return resultBean.success();
    }

    @RequestMapping("gettoken")
    public ResultBean gettoken(String token) {
        ResultBean resultBean = new ResultBean();
        logger.error(token);

        resultBean.setMsg("success");
        resultBean.setCode(0);
        resultBean.setData("成功");
        return resultBean;
    }

    @RequestMapping("getOrder")
    public ResultBean getOrder(String start,String end,Integer pageNo) throws Exception {
        int pageNow = pageNo == null ? 1 : pageNo;
//        String startTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER);
//        String endTime = DateUtils.format(DateUtils.addMinutes(new Date(), 10), DateUtils.DATE_TIME_PATTERN_OTHER);
        String jd_param_json = "{\"" +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_begin\":\"" + start + "\",\"") : "") +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_end\":\"" + end + "\",\"") : "") +
                "pageNo\":\"" + pageNow + "\"," +
                // "\"businessType_list\":\"1,2,3,4,5,6,7,8,9,10\"," +
                "\"pageSize\":\"" + pageSize + "\"}";

        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey);
        webReqeustDTO.setFormat("json");
            webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        webReqeustDTO.setToken(token);
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret);
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", token);
        params.put("app_key", appKey);
        params.put("timestamp", StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
            params.put("jd_param_json", jd_param_json);
//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/order/es/query", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            String encryptData = (String) jsonObject.get("encryptData");
            String key = appSecret.substring(0, 16);
            String iv = appSecret.substring(16, 32);
            String json = AESUtils.decryptAES(encryptData, key, iv);
            JSONObject jsonResult = JSON.parseObject(json);
            String code1 = jsonResult.get("code").toString();
            if (code1.equals("0")) {
                JSONObject qqqq = JSON.parseObject((String) jsonResult.get("result"));
                JSONArray gggg = qqqq.getJSONArray("resultList");
                if (!CollectionUtil.isEmpty(gggg)) {
                    for (Object o : gggg) {
                        JSONObject wwww = (JSONObject) o;
                        String mobile = (String) wwww.get("buyerMobile");
                        String deliveryStationName = (String) wwww.get("deliveryStationName");
                        String username = (String) wwww.get("buyerFullName");
                        String buyerCityName = (String) wwww.get("buyerCityName");
                        String buyerCountryName = (String) wwww.get("buyerCountryName");
                        String buyerFullAddress = (String) wwww.get("buyerFullAddress");
                        BigDecimal buyerLat = (BigDecimal) wwww.get("buyerLat");
                        BigDecimal buyerLng = (BigDecimal) wwww.get("buyerLng");
                        Integer orderStatus = (Integer) wwww.get("orderStatus");
                        Long orderId = (Long) wwww.get("orderId");
                        Date orderPreEndDeliveryTime = wwww.getDate("orderPreEndDeliveryTime");
                        Integer orderNum = 0;
                        Long storeId = null;
                        // if (buyerLat != null && buyerLng != null) {
                        //     storeId = storeIdUtil.determineByWeiLan(buyerLat, buyerLng, buyerCityName, buyerCountryName, buyerFullAddress, buyerFullAddress,0);
                        // } else {
                            storeId = StoreIdUtil.determineStoreId(buyerCityName,buyerCityName, buyerCountryName, buyerFullAddress);
                        // }

                        Double orderTotalMoney = new BigDecimal((Integer) wwww.get("orderTotalMoney")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
                        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId.toString());
                        if (null != szmCOrderMain) {
                            if (szmCOrderMain.getOrderStatus() >= 1) {
                            // 看看订单状态是不是取消了
                                if (orderStatus.equals(20010) || orderStatus.equals(20020) || orderStatus.equals(20030) || orderStatus.equals(20040)) {
                                    SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId.toString());
                                    if (null == smzCOrderReturns) {
                                        smzCOrderReturns = new SmzCOrderReturns();
                                        smzCOrderReturns.setOrderReturnsDelStart(1);
                                        smzCOrderReturns.setProcessstate((orderStatus.equals(20020) || orderStatus.equals(20040)) ? 1 : 0);//退款状态
                                        smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                        smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                        smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                        smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                        smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                        smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                        smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                        smzCOrderReturns.setOrderDetailsId(orderId.toString());
                                        smzCOrderReturns.setReturnsType("退款");
                                        smzCOrderReturns.setLogisticsDescription("其他");
                                        smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                        szmCOrderMain.setOrderStatus(8);
                                        szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                        
                                        StoreMsg storeMsg = new StoreMsg();
                                        storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                                        storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                                        storeMsg.setModelUrl("orderAdmin");//模块地址
                                        storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                                        StringBuffer stringBuffer = new StringBuffer();
                                        stringBuffer.append("您的客户 ");
                                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                            stringBuffer.append(szmCOrderMain.getUserName());
                                        } else {
                                            stringBuffer.append(szmCUserinfo.getR3());
                                        }
                                        stringBuffer.append(" 于");
                                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
                                        if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                            stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                        } else {
                                            stringBuffer.append("发起了订单退款，请点击前往处理！");
                                        }
                                        storeMsg.setContent(stringBuffer.toString());//内容
                                        storeMsg.setReadState(0);//已读 1 未读 0
                                        storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                        storeMsg.setDelState(0);//删除状态
                                        storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                                        storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                                        storeMsgMapper.insert(storeMsg);
                                    } else {
                                        if(orderStatus.equals(20020) ||  orderStatus.equals(20040)){
                                            smzCOrderReturns.setProcessstate(1);//退款状态
                                            smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                            szmCOrderMain.setOrderStatus(8);
                                            szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                        }
                                    }
                                }
                            }
                            continue;
                        }
                        // 只有付过款的订单，才能进入
                        if (!orderStatus.equals(31020) && !orderStatus.equals(41000)
                                && !orderStatus.equals(32000) && !orderStatus.equals(33040) && !orderStatus.equals(33060) && !orderStatus.equals(90000)) {
                            continue;
                        }
                        SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                        if (null != szmCUser) {
                            // if (StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setOrdersource(1);
                            szmCUserMapper.updateByPrimaryKey(szmCUser);
                            // }
                        } else {
                            // 创建用户
                            szmCUser = new SzmCUser();
                            szmCUser.setUserMobile(mobile);
                            szmCUser.setUserNickname(username);
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setBindingTime(new Date());
                            szmCUser.setOrdersource(1);
                            ResultBean resultBean1 = szmCUserService.addUser(szmCUser);
                            if (1 != resultBean1.getCode()) {
                                return resultBean1;
                            }
                        }

                        SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                        SzmCAddress szmCAddress = new SzmCAddress();
                        szmCAddress.setUserId(szmCUserExtra.getUserId());
                        szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                        szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
//                        szmCAddress.setProvince(oldUser.getProvince());
                        szmCAddress.setCity(buyerCityName);
                        szmCAddress.setArea(buyerCountryName);
                        szmCAddress.setStreet(buyerFullAddress);
                        szmCAddress.setIsDefaultAddress(0);
                        szmCAddress.setState(0);
                        szmCAddress.setR1("0");
                        szmCAddress.setR2("1");

                        szmCAddress.setR5(buyerLat + "," + buyerLng);
                        szmCAddressMapper.insertAddress(szmCAddress);
                        // 开始组建订单
                        JSONArray product = wwww.getJSONArray("product");
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            orderNum += eee.getInteger("skuCount");
                        }
                        //设置商品信息
                        SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                        szmCOrderMainlianying.setAppkey(appKey);
                        szmCOrderMainlianying.setApptoken(token);
                        szmCOrderMainlianying.setZuobiao(buyerLat + "," + buyerLng);
                        szmCOrderMainlianying.setLat(buyerLat);
                        szmCOrderMainlianying.setLon(buyerLng);
                        szmCOrderMainlianying.setYuyuetime(orderPreEndDeliveryTime);
                        szmCOrderMainlianying.setGroup(0);
                        szmCOrderMainlianying.setUpPrice(0D);
                        szmCOrderMainlianying.setRoyalty(0D);
                        szmCOrderMainlianying.setRemind(0);
                        szmCOrderMainlianying.setCdTypeMoney(0D);
                        szmCOrderMainlianying.setCdMoneyType(0);
                        szmCOrderMainlianying.setIsReturn(0);
                        szmCOrderMainlianying.setIsSms(0);
                        szmCOrderMainlianying.setIsForward(1);
                        szmCOrderMainlianying.setIsInvoice(0);
                        szmCOrderMainlianying.setBucketPrice(0D);
                        szmCOrderMainlianying.setYfMoney(0D);
                        szmCOrderMainlianying.setBack(0);
                        szmCOrderMainlianying.setOrderDiscounts(0D);
                        szmCOrderMainlianying.setFreightPayable(0D);
                        szmCOrderMainlianying.setCdMoney(0D);
                        szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                        szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                        szmCOrderMainlianying.setCreateTime(new Date());
                        szmCOrderMainlianying.setPayTime(new Date());
                        szmCOrderMainlianying.setOrderNum(orderId.toString());
                        szmCOrderMainlianying.setUserName(username);
                        szmCOrderMainlianying.setUserPhone(mobile);
                        szmCOrderMainlianying.setUserAddress(buyerFullAddress);
                        // szmCOrderMainlianying.setOrderMoney(orderTotalMoney);
                        szmCOrderMainlianying.setOrderMoney(0D);
                        szmCOrderMainlianying.setR1("0");
                        szmCOrderMainlianying.setPayNum(orderId.toString());
                        szmCOrderMainlianying.setOrderStatus(2);
                        szmCOrderMainlianying.setIsReplenishment(0);
                        szmCOrderMainlianying.setUserContent(deliveryStationName);
                        szmCOrderMainlianying.setOrderDelState(0);
                        szmCOrderMainlianying.setR3("0");
                        szmCOrderMainlianying.setR4("[]");
                        szmCOrderMainlianying.setBucketBeans("[]");
                        szmCOrderMainlianying.setR5(orderNum.toString());
                        szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                        szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                        szmCOrderMainlianying.setPaymentModeId(8L);
                        szmCOrderMainlianying.setDaike(0);
                        szmCOrderMainlianying.setRemind(1);
                        szmCOrderMainlianying.setOrdersource(1);
                        
                        // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                        try {
                            OrderSource orderSource = orderSourceService.selectByPrimaryKey(1);
                            if (orderSource != null && orderSource.getSettlementCycle() != null) {
                                szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                            }
                        } catch (Exception e) {
                            logger.error("获取订单来源结算周期失败", e);
                        }
                        
                        szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);

                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            Long skuId = eee.getLong("skuId");
                            // 日志
                            logger.error("skuId: "+ skuId);
                            SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                            

                            // 根据ordersource和skuId查询关联的product_id并计算价格
                            Double orderDetailsProductPrice = 0D;
                            String unitPrice = "0";
                            String totalPrice = "0";
                            String name = (String) eee.get("skuName");
                            String image = "";
                            try {
                                // 查询关联关系
                                OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                                    Long.valueOf(szmCOrderMainlianying.getOrdersource()), skuId.toString());

                                // 如果找不到关联关系，自动新增一条记录
                                if (connect == null) {
                                    connect = new OrderSourceConnect();
                                    connect.setUnionCode(skuId.toString());
                                    connect.setName((String)eee.get("skuName"));
                                    connect.setProductNewId(null); // product_new_id为空，后续可以在后台补充
                                    connect.setOrderSourceId(Long.valueOf(szmCOrderMainlianying.getOrdersource()));
                                    connect.setCreateTime(new Date());
                                    connect.setUpdateTime(new Date());

                                    try {
                                        orderSourceConnectMapper.insert(connect);
                                        logger.error("自动新增关联关系：ordersource={}, skuId={}, name={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getName());
                                    } catch (Exception insertEx) {
                                        logger.error("自动新增关联关系失败：ordersource={}, skuId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, insertEx);
                                    }
                                }

                                if (connect != null && connect.getProductNewId() != null) {
                                    // 根据product_id查询产品价格
                                    SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                                    if (productInfo != null && productInfo.getPrice() != null) {
                                        // 使用price字段而不是sellprice
                                        BigDecimal basePrice = productInfo.getPrice();
                                        Integer quantity = eee.getInteger("skuCount");
                                        name = productInfo.getProductTitle();
                                        image = productInfo.getR1();
                                        // 根据数量计算单位配送费
                                        BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                        // 单品最终单价 = 基础价格 + 单位配送费
                                        BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                        // 总价 = 单品最终单价 * 数量
                                        orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity)).doubleValue();
                                        unitPrice = finalUnitPrice.toString();
                                        totalPrice = orderDetailsProductPrice.toString();

                                        logger.error("skuId: {}, productId: {}, basePrice: {}, unitDeliveryFee: {}, finalUnitPrice: {}, quantity: {}, totalPrice: {}",
                                            skuId, connect.getProductNewId(), basePrice, unitDeliveryFee, finalUnitPrice, quantity, orderDetailsProductPrice);
                                    } else {
                                        logger.error("关联关系存在但product_new_id为空或产品价格为空：ordersource={}, skuId={}, productNewId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getProductNewId());
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("计算订单详情价格失败，skuId: {}, ordersource: {}", skuId, szmCOrderMainlianying.getOrdersource(), e);
                            }

                            smzCOrderDetailLianying.setSource(0);
                            smzCOrderDetailLianying.setProductModelId(null);
                            smzCOrderDetailLianying.setProductSkuname(name);
                            smzCOrderDetailLianying.setProductSkuimg(image);
                            smzCOrderDetailLianying.setOrderProductNum((Integer) eee.getInteger("skuCount"));
                            // 设置计算出的价格
                            smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                            smzCOrderDetailLianying.setR1(unitPrice);
                            smzCOrderDetailLianying.setOrderMainId(orderId.toString());
                            smzCOrderDetailLianying.setR5(orderId + "1");//子订单编号
                            smzCOrderDetailLianying.setR4(totalPrice);
                            smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                            smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                            smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                            smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                        }
                        // 通知用户
                        szmBOrderService.jieDanSms(orderId.toString());
                        // 通知商家
                        StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                        if (storeSmsInfo != null) {
                            SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                            if (storeSmsInfo.getResidueNum() > 0) {
                                SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                                if (smsRelevance != null && smsRelevance.getState() == 1) {
                                    String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                                    UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                                    storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                                    storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                                    storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                                    SmsRecord smsRecord = new SmsRecord();
                                    smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                                    SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                                    smsRecord.setContent(smsMaster.getName());
                                    smsRecordMapper.insert(smsRecord);
                                }
                            } else {
                                RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                            }
                        }
                        LoggerUtil.info("新增订单通知");
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("新订单通知");//模块名称
                        storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMainlianying.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                        stringBuffer.append("下了一笔订单，请点击前往处理！");
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                        storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                        storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                        storeMsgMapper.insert(storeMsg);
                        try {
                            // 自动派单送水员
                            Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                            szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                            logger.error("送水员围栏判断，找到送水员id:{}", deliveryUserId);
                        if (deliveryUserId != null) {
                                szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                            }
                        } catch (Exception e) {
                            logger.error("自动派单送水员失败", e);
                        }
                    }
                    pageNow = pageNow + 1;
                    this.getOrder(start, end, pageNow);
                }
                return new ResultBean().success();
            } else {
                return new ResultBean().error(jsonResult.get("msg"));
            }
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }

    @RequestMapping("getOrder1")
    public ResultBean getOrder1(String start,String end,Integer pageNo) throws Exception {
        int pageNow = pageNo == null ? 1 : pageNo;
//        String startTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER);
//        String endTime = DateUtils.format(DateUtils.addMinutes(new Date(), 10), DateUtils.DATE_TIME_PATTERN_OTHER);
        String jd_param_json = "{\"" +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_begin\":\"" + start + "\",\"") : "") +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_end\":\"" + end + "\",\"") : "") +
                "pageNo\":\"" + pageNow + "\",\"pageSize\":\"" + pageSize + "\"}";

        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey1);
        webReqeustDTO.setFormat("json");
            webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        webReqeustDTO.setToken(token1);
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret1);
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", token1);
        params.put("app_key", appKey1);
        params.put("timestamp", StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
            params.put("jd_param_json", jd_param_json);
//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/order/es/query", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            String encryptData = (String) jsonObject.get("encryptData");
            String key = appSecret1.substring(0, 16);
            String iv = appSecret1.substring(16, 32);
            String json = AESUtils.decryptAES(encryptData, key, iv);
            JSONObject jsonResult = JSON.parseObject(json);
            String code1 = jsonResult.get("code").toString();
            if (code1.equals("0")) {
                JSONObject qqqq = JSON.parseObject((String) jsonResult.get("result"));
                JSONArray gggg = qqqq.getJSONArray("resultList");
                if (!CollectionUtil.isEmpty(gggg)) {
                    for (Object o : gggg) {
                        JSONObject wwww = (JSONObject) o;
                        String mobile = (String) wwww.get("buyerMobile");
                        String deliveryStationName = (String) wwww.get("deliveryStationName");
                        String username = (String) wwww.get("buyerFullName");
                        String buyerCityName = (String) wwww.get("buyerCityName");
                        String buyerCountryName = (String) wwww.get("buyerCountryName");
                        String buyerFullAddress = (String) wwww.get("buyerFullAddress");
                        BigDecimal buyerLat = (BigDecimal) wwww.get("buyerLat");
                        BigDecimal buyerLng = (BigDecimal) wwww.get("buyerLng");
                        Integer orderStatus = (Integer) wwww.get("orderStatus");
                        Long orderId = (Long) wwww.get("orderId");
                        Date orderPreEndDeliveryTime = wwww.getDate("orderPreEndDeliveryTime");
                        Integer orderNum = 0;
                        Long storeId = null;
                        // if (buyerLat != null && buyerLng != null) {
                        //     storeId = storeIdUtil.determineByWeiLan(buyerLat, buyerLng, buyerCityName, buyerCountryName, buyerFullAddress, buyerFullAddress,0);
                        // } else {
                            storeId = StoreIdUtil.determineStoreId(buyerCityName,buyerCityName, buyerCountryName, buyerFullAddress);
                        // }
                        Double orderTotalMoney = new BigDecimal((Integer) wwww.get("orderTotalMoney")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
                        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId.toString());
                        if (null != szmCOrderMain) {
                            if (szmCOrderMain.getOrderStatus() >= 1) {
                                // 看看订单状态是不是取消了
                                    if (orderStatus.equals(20010) || orderStatus.equals(20020) || orderStatus.equals(20030) || orderStatus.equals(20040)) {
                                        SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId.toString());
                                        if (null == smzCOrderReturns) {
                                            smzCOrderReturns = new SmzCOrderReturns();
                                            smzCOrderReturns.setOrderReturnsDelStart(1);
                                            smzCOrderReturns.setProcessstate((orderStatus.equals(20020) || orderStatus.equals(20040)) ? 1 : 0);//退款状态
                                            smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                            smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                            smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                            smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                            smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                            smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                            smzCOrderReturns.setOrderDetailsId(orderId.toString());
                                            smzCOrderReturns.setReturnsType("退款");
                                            smzCOrderReturns.setLogisticsDescription("其他");
                                            smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                            szmCOrderMain.setOrderStatus(8);
                                            szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            
                                            StoreMsg storeMsg = new StoreMsg();
                                            storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                                            storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                                            storeMsg.setModelUrl("orderAdmin");//模块地址
                                            storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                                            StringBuffer stringBuffer = new StringBuffer();
                                            stringBuffer.append("您的客户 ");
                                            SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                                stringBuffer.append(szmCOrderMain.getUserName());
                                            } else {
                                                stringBuffer.append(szmCUserinfo.getR3());
                                            }
                                            stringBuffer.append(" 于");
                                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
                                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                                stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                            } else {
                                                stringBuffer.append("发起了订单退款，请点击前往处理！");
                                            }
                                            storeMsg.setContent(stringBuffer.toString());//内容
                                            storeMsg.setReadState(0);//已读 1 未读 0
                                            storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                            storeMsg.setDelState(0);//删除状态
                                            storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                                            storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                                            storeMsgMapper.insert(storeMsg);
                                        } else {
                                            if(orderStatus.equals(20020) ||  orderStatus.equals(20040)){
                                                smzCOrderReturns.setProcessstate(1);//退款状态
                                                smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                                szmCOrderMain.setOrderStatus(8);
                                                szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            }
                                        }
                                    }
                                }
                                continue;
                        }
                        // 只有付过款的订单，才能进入
                        if (!orderStatus.equals(31020) && !orderStatus.equals(41000)
                                && !orderStatus.equals(32000) && !orderStatus.equals(33040) && !orderStatus.equals(33060) && !orderStatus.equals(90000)) {
                            continue;
                        }
                        SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                        if (null != szmCUser) {
                            // if (StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setOrdersource(1);
                            szmCUserMapper.updateByPrimaryKey(szmCUser);
                            // }
                        } else {
                            // 创建用户
                            szmCUser = new SzmCUser();
                            szmCUser.setUserMobile(mobile);
                            szmCUser.setUserNickname(username);
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setBindingTime(new Date());
                            szmCUser.setOrdersource(1);
                            ResultBean resultBean1 = szmCUserService.addUser(szmCUser);
                            if (1 != resultBean1.getCode()) {
                                return resultBean1;
                            }
                        }

                        SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                        SzmCAddress szmCAddress = new SzmCAddress();
                        szmCAddress.setUserId(szmCUserExtra.getUserId());
                        szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                        szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
//                        szmCAddress.setProvince(oldUser.getProvince());
                        szmCAddress.setCity(buyerCityName);
                        szmCAddress.setArea(buyerCountryName);
                        szmCAddress.setStreet(buyerFullAddress);
                        szmCAddress.setIsDefaultAddress(0);
                        szmCAddress.setState(0);
                        szmCAddress.setR1("0");
                        szmCAddress.setR2("1");

                        szmCAddress.setR5(buyerLat + "," + buyerLng);
                        szmCAddressMapper.insertAddress(szmCAddress);
                        // 开始组建订单

                        JSONArray product = wwww.getJSONArray("product");
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            orderNum += eee.getInteger("skuCount");
                        }
                        //设置商品信息
                        SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                        szmCOrderMainlianying.setAppkey(appKey1);
                        szmCOrderMainlianying.setApptoken(token1);
                        szmCOrderMainlianying.setZuobiao(buyerLat + "," + buyerLng);
                        szmCOrderMainlianying.setLat(buyerLat);
                        szmCOrderMainlianying.setLon(buyerLng);
                        szmCOrderMainlianying.setYuyuetime(orderPreEndDeliveryTime);
                        szmCOrderMainlianying.setGroup(0);
                        szmCOrderMainlianying.setUpPrice(0D);
                        szmCOrderMainlianying.setRoyalty(0D);
                        szmCOrderMainlianying.setRemind(0);
                        szmCOrderMainlianying.setCdTypeMoney(0D);
                        szmCOrderMainlianying.setCdMoneyType(0);
                        szmCOrderMainlianying.setIsReturn(0);
                        szmCOrderMainlianying.setIsSms(0);
                        szmCOrderMainlianying.setIsForward(1);
                        szmCOrderMainlianying.setIsInvoice(0);
                        szmCOrderMainlianying.setBucketPrice(0D);
                        szmCOrderMainlianying.setYfMoney(0D);
                        szmCOrderMainlianying.setBack(0);
                        szmCOrderMainlianying.setOrderDiscounts(0D);
                        szmCOrderMainlianying.setFreightPayable(0D);
                        szmCOrderMainlianying.setCdMoney(0D);
                        szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                        szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                        szmCOrderMainlianying.setCreateTime(new Date());
                        szmCOrderMainlianying.setPayTime(new Date());
                        szmCOrderMainlianying.setOrderNum(orderId.toString());
                        szmCOrderMainlianying.setUserName(username);
                        szmCOrderMainlianying.setUserPhone(mobile);
                        szmCOrderMainlianying.setUserAddress(buyerFullAddress);
                        // szmCOrderMainlianying.setOrderMoney(orderTotalMoney);
                        szmCOrderMainlianying.setOrderMoney(0D);
                        szmCOrderMainlianying.setR1("0");
                        szmCOrderMainlianying.setPayNum(orderId.toString());
                        szmCOrderMainlianying.setOrderStatus(2);
                        szmCOrderMainlianying.setIsReplenishment(0);
                        szmCOrderMainlianying.setUserContent(deliveryStationName);
                        szmCOrderMainlianying.setOrderDelState(0);
                        szmCOrderMainlianying.setR3("0");
                        szmCOrderMainlianying.setR4("[]");
                        szmCOrderMainlianying.setBucketBeans("[]");
                        szmCOrderMainlianying.setR5(orderNum.toString());
                        szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                        szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                        szmCOrderMainlianying.setPaymentModeId(8L);
                        szmCOrderMainlianying.setDaike(0);
                        szmCOrderMainlianying.setRemind(1);
                        szmCOrderMainlianying.setOrdersource(1);
                        
                        // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                        try {
                            OrderSource orderSource = orderSourceService.selectByPrimaryKey(1);
                            if (orderSource != null && orderSource.getSettlementCycle() != null) {
                                szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                            }
                        } catch (Exception e) {
                            logger.error("获取订单来源结算周期失败", e);
                        }
                        
                        szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            Long skuId = eee.getLong("skuId");

                            // 根据ordersource和skuId查询关联的product_id并计算价格
                            Double orderDetailsProductPrice = 0D;
                            String unitPrice = "0";
                            String totalPrice = "0";

                            try {
                                // 查询关联关系
                                OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                                    Long.valueOf(szmCOrderMainlianying.getOrdersource()), skuId.toString());

                                // 如果找不到关联关系，自动新增一条记录
                                if (connect == null) {
                                    connect = new OrderSourceConnect();
                                    connect.setUnionCode(skuId.toString());
                                    connect.setName((String) eee.get("skuName"));
                                    connect.setProductNewId(null); // product_new_id为空，后续可以在后台补充
                                    connect.setOrderSourceId(Long.valueOf(szmCOrderMainlianying.getOrdersource()));
                                    connect.setCreateTime(new Date());
                                    connect.setUpdateTime(new Date());

                                    try {
                                        orderSourceConnectMapper.insert(connect);
                                        logger.error("getOrder1 - 自动新增关联关系：ordersource={}, skuId={}, name={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getName());
                                    } catch (Exception insertEx) {
                                        logger.error("getOrder1 - 自动新增关联关系失败：ordersource={}, skuId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, insertEx);
                                    }
                                }

                                if (connect != null && connect.getProductNewId() != null) {
                                    // 根据product_id查询产品价格
                                    SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                                    if (productInfo != null && productInfo.getPrice() != null) {
                                        // 使用price字段而不是sellprice
                                        BigDecimal basePrice = productInfo.getPrice();
                                        Integer quantity = eee.getInteger("skuCount");

                                        // 根据数量计算单位配送费
                                        BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                        // 单品最终单价 = 基础价格 + 单位配送费
                                        BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                        // 总价 = 单品最终单价 * 数量
                                        orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity)).doubleValue();
                                        unitPrice = finalUnitPrice.toString();
                                        totalPrice = orderDetailsProductPrice.toString();

                                        logger.error("getOrder1 - skuId: {}, productId: {}, basePrice: {}, unitDeliveryFee: {}, finalUnitPrice: {}, quantity: {}, totalPrice: {}",
                                            skuId, connect.getProductNewId(), basePrice, unitDeliveryFee, finalUnitPrice, quantity, orderDetailsProductPrice);
                                    } else {
                                        logger.error("getOrder1 - 关联关系存在但product_new_id为空或产品价格为空：ordersource={}, skuId={}, productNewId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getProductNewId());
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("getOrder1 - 计算订单详情价格失败，skuId: {}, ordersource: {}", skuId, szmCOrderMainlianying.getOrdersource(), e);
                            }

                            SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                            smzCOrderDetailLianying.setSource(0);
                            smzCOrderDetailLianying.setProductModelId(null);
                            smzCOrderDetailLianying.setProductSkuname((String) eee.get("skuName"));
                            smzCOrderDetailLianying.setProductSkuimg("");
                            smzCOrderDetailLianying.setOrderProductNum((Integer) eee.getInteger("skuCount"));
                            // 设置计算出的价格
                            smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                            smzCOrderDetailLianying.setR1(unitPrice);
                            smzCOrderDetailLianying.setOrderMainId(orderId.toString());
                            smzCOrderDetailLianying.setR5(orderId + "1");//子订单编号
                            smzCOrderDetailLianying.setR4(totalPrice);
                            smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                            smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                            smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                            smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                        }
                        // 通知用户
                        szmBOrderService.jieDanSms(orderId.toString());
                        // 通知商家
                        StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                        if (storeSmsInfo != null) {
                            SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                            if (storeSmsInfo.getResidueNum() > 0) {
                                SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                                if (smsRelevance != null && smsRelevance.getState() == 1) {
                                    String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                                    UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                                    storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                                    storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                                    storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                                    SmsRecord smsRecord = new SmsRecord();
                                    smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                                    SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                                    smsRecord.setContent(smsMaster.getName());
                                    smsRecordMapper.insert(smsRecord);
                                }
                            } else {
                                RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                            }
                        }
                        LoggerUtil.info("新增订单通知");
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("新订单通知");//模块名称
                        storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMainlianying.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                        stringBuffer.append("下了一笔订单，请点击前往处理！");
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                        storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                        storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                        storeMsgMapper.insert(storeMsg);
                        try {
                            // 自动派单送水员
                            Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                            szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                            logger.error("送水员围栏判断，找到送水员id:{}", deliveryUserId);
                        if (deliveryUserId != null) {
                                szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                            }
                        } catch (Exception e) {
                            logger.error("自动派单送水员失败", e);
                        }
                    }
                    pageNow = pageNow + 1;
                    this.getOrder1(start, end, pageNow);
                }
                return new ResultBean().success();
            } else {
                return new ResultBean().error(jsonResult.get("msg"));
            }
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }


    @RequestMapping("getOrder2")
    public ResultBean getOrder2(String start,String end,Integer pageNo) throws Exception {
        int pageNow = pageNo == null ? 1 : pageNo;
//        String startTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER);
//        String endTime = DateUtils.format(DateUtils.addMinutes(new Date(), 10), DateUtils.DATE_TIME_PATTERN_OTHER);
        String jd_param_json = "{\"" +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_begin\":\"" + start + "\",\"") : "") +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_end\":\"" + end + "\",\"") : "") +
                "pageNo\":\"" + pageNow + "\",\"pageSize\":\"" + pageSize + "\"}";

        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey2);
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        webReqeustDTO.setToken(token2);
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret2);
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", token2);
        params.put("app_key", appKey2);
        params.put("timestamp", StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);
//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/order/es/query", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            String encryptData = (String) jsonObject.get("encryptData");
            String key = appSecret2.substring(0, 16);
            String iv = appSecret2.substring(16, 32);
            String json = AESUtils.decryptAES(encryptData, key, iv);
            JSONObject jsonResult = JSON.parseObject(json);
            String code1 = jsonResult.get("code").toString();
            if (code1.equals("0")) {
                JSONObject qqqq = JSON.parseObject((String) jsonResult.get("result"));
                JSONArray gggg = qqqq.getJSONArray("resultList");
                if (!CollectionUtil.isEmpty(gggg)) {
                    for (Object o : gggg) {
                        JSONObject wwww = (JSONObject) o;
                        String mobile = (String) wwww.get("buyerMobile");
                        String deliveryStationName = (String) wwww.get("deliveryStationName");
                        String username = (String) wwww.get("buyerFullName");
                        String buyerCityName = (String) wwww.get("buyerCityName");
                        String buyerCountryName = (String) wwww.get("buyerCountryName");
                        String buyerFullAddress = (String) wwww.get("buyerFullAddress");
                        BigDecimal buyerLat = (BigDecimal) wwww.get("buyerLat");
                        BigDecimal buyerLng = (BigDecimal) wwww.get("buyerLng");
                        Integer orderStatus = (Integer) wwww.get("orderStatus");
                        Date orderPreEndDeliveryTime = wwww.getDate("orderPreEndDeliveryTime");
                        Long orderId = (Long) wwww.get("orderId");
                        Integer orderNum = 0;
                        Long storeId = null;
                        // if (buyerLat != null && buyerLng != null) {
                        //     storeId = storeIdUtil.determineByWeiLan(buyerLat, buyerLng, buyerCityName, buyerCountryName, buyerFullAddress, buyerFullAddress,0);
                        // } else {
                            storeId = StoreIdUtil.determineStoreId(buyerCityName,buyerCityName, buyerCountryName, buyerFullAddress);
                        // }
                        Double orderTotalMoney = new BigDecimal((Integer) wwww.get("orderTotalMoney")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
                        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId.toString());
                        if (null != szmCOrderMain) {
                            if (szmCOrderMain.getOrderStatus() >= 1) {
                                // 看看订单状态是不是取消了
                                    if (orderStatus.equals(20010) || orderStatus.equals(20020) || orderStatus.equals(20030) || orderStatus.equals(20040)) {
                                        SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId.toString());
                                        if (null == smzCOrderReturns) {
                                            smzCOrderReturns = new SmzCOrderReturns();
                                            smzCOrderReturns.setOrderReturnsDelStart(1);
                                            smzCOrderReturns.setProcessstate((orderStatus.equals(20020) || orderStatus.equals(20040)) ? 1 : 0);//退款状态
                                            smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                            smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                            smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                            smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                            smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                            smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                            smzCOrderReturns.setOrderDetailsId(orderId.toString());
                                            smzCOrderReturns.setReturnsType("退款");
                                            smzCOrderReturns.setLogisticsDescription("其他");
                                            smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                            szmCOrderMain.setOrderStatus(8);
                                            szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            
                                            StoreMsg storeMsg = new StoreMsg();
                                            storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                                            storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                                            storeMsg.setModelUrl("orderAdmin");//模块地址
                                            storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                                            StringBuffer stringBuffer = new StringBuffer();
                                            stringBuffer.append("您的客户 ");
                                            SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                                stringBuffer.append(szmCOrderMain.getUserName());
                                            } else {
                                                stringBuffer.append(szmCUserinfo.getR3());
                                            }
                                            stringBuffer.append(" 于");
                                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
                                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                                stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                            } else {
                                                stringBuffer.append("发起了订单退款，请点击前往处理！");
                                            }
                                            storeMsg.setContent(stringBuffer.toString());//内容
                                            storeMsg.setReadState(0);//已读 1 未读 0
                                            storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                            storeMsg.setDelState(0);//删除状态
                                            storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                                            storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                                            storeMsgMapper.insert(storeMsg);
                                        } else {
                                            if(orderStatus.equals(20020) ||  orderStatus.equals(20040)){
                                                smzCOrderReturns.setProcessstate(1);//退款状态
                                                smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                                szmCOrderMain.setOrderStatus(8);
                                                szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            }
                                        }
                                    }
                                }
                                continue;
                        }
                        // 只有付过款的订单，才能进入
                        if (!orderStatus.equals(31020) && !orderStatus.equals(41000)
                                && !orderStatus.equals(32000) && !orderStatus.equals(33040) && !orderStatus.equals(33060) && !orderStatus.equals(90000)) {
                            continue;
                        }
                        SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                        if (null != szmCUser) {
                            // if (StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                                szmCUser.setR2(storeId.toString());
                                szmCUser.setStoreId(storeId);
                                szmCUser.setOrdersource(1);
                                szmCUserMapper.updateByPrimaryKey(szmCUser);
                            // }
                        } else {
                            // 创建用户
                            szmCUser = new SzmCUser();
                            szmCUser.setUserMobile(mobile);
                            szmCUser.setUserNickname(username);
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setOrdersource(1);
                            szmCUser.setBindingTime(new Date());
                            ResultBean resultBean1 = szmCUserService.addUser(szmCUser);
                            if (1 != resultBean1.getCode()) {
                                return resultBean1;
                            }
                        }

                        SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                        SzmCAddress szmCAddress = new SzmCAddress();
                        szmCAddress.setUserId(szmCUserExtra.getUserId());
                        szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                        szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
//                        szmCAddress.setProvince(oldUser.getProvince());
                        szmCAddress.setCity(buyerCityName);
                        szmCAddress.setArea(buyerCountryName);
                        szmCAddress.setStreet(buyerFullAddress);
                        szmCAddress.setIsDefaultAddress(0);
                        szmCAddress.setState(0);
                        szmCAddress.setR1("0");
                        szmCAddress.setR2("1");

                        szmCAddress.setR5(buyerLat + "," + buyerLng);
                        szmCAddressMapper.insertAddress(szmCAddress);
                        // 开始组建订单

                        JSONArray product = wwww.getJSONArray("product");
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            orderNum += eee.getInteger("skuCount");
                        }
                        //设置商品信息
                        SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                        szmCOrderMainlianying.setAppkey(appKey2);
                        szmCOrderMainlianying.setApptoken(token2);
                        szmCOrderMainlianying.setZuobiao(buyerLat + "," + buyerLng);
                        szmCOrderMainlianying.setLat(buyerLat);
                        szmCOrderMainlianying.setLon(buyerLng);
                        szmCOrderMainlianying.setYuyuetime(orderPreEndDeliveryTime);
                        szmCOrderMainlianying.setGroup(0);
                        szmCOrderMainlianying.setUpPrice(0D);
                        szmCOrderMainlianying.setRoyalty(0D);
                        szmCOrderMainlianying.setRemind(0);
                        szmCOrderMainlianying.setCdTypeMoney(0D);
                        szmCOrderMainlianying.setCdMoneyType(0);
                        szmCOrderMainlianying.setIsReturn(0);
                        szmCOrderMainlianying.setIsSms(0);
                        szmCOrderMainlianying.setIsForward(1);
                        szmCOrderMainlianying.setIsInvoice(0);
                        szmCOrderMainlianying.setBucketPrice(0D);
                        szmCOrderMainlianying.setYfMoney(0D);
                        szmCOrderMainlianying.setBack(0);
                        szmCOrderMainlianying.setOrderDiscounts(0D);
                        szmCOrderMainlianying.setFreightPayable(0D);
                        szmCOrderMainlianying.setCdMoney(0D);
                        szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                        szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                        szmCOrderMainlianying.setCreateTime(new Date());
                        szmCOrderMainlianying.setPayTime(new Date());
                        szmCOrderMainlianying.setOrderNum(orderId.toString());
                        szmCOrderMainlianying.setUserName(username);
                        szmCOrderMainlianying.setUserPhone(mobile);
                        szmCOrderMainlianying.setUserAddress(buyerFullAddress);
                        // szmCOrderMainlianying.setOrderMoney(orderTotalMoney);
                        szmCOrderMainlianying.setOrderMoney(0D);
                        szmCOrderMainlianying.setR1("0");
                        szmCOrderMainlianying.setPayNum(orderId.toString());
                        szmCOrderMainlianying.setOrderStatus(2);
                        szmCOrderMainlianying.setIsReplenishment(0);
                        szmCOrderMainlianying.setUserContent(deliveryStationName);
                        szmCOrderMainlianying.setOrderDelState(0);
                        szmCOrderMainlianying.setR3("0");
                        szmCOrderMainlianying.setR4("[]");
                        szmCOrderMainlianying.setBucketBeans("[]");
                        szmCOrderMainlianying.setR5(orderNum.toString());
                        szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                        szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                        szmCOrderMainlianying.setPaymentModeId(8L);
                        szmCOrderMainlianying.setDaike(0);
                        szmCOrderMainlianying.setRemind(1);
                        szmCOrderMainlianying.setOrdersource(1);
                        
                        // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                        try {
                            OrderSource orderSource = orderSourceService.selectByPrimaryKey(1);
                            if (orderSource != null && orderSource.getSettlementCycle() != null) {
                                szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                            }
                        } catch (Exception e) {
                            logger.error("获取订单来源结算周期失败", e);
                        }
                        
                        szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            Long skuId = eee.getLong("skuId");

                            // 根据ordersource和skuId查询关联的product_id并计算价格
                            Double orderDetailsProductPrice = 0D;
                            String unitPrice = "0";
                            String totalPrice = "0";

                            try {
                                // 查询关联关系
                                OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                                    Long.valueOf(szmCOrderMainlianying.getOrdersource()), skuId.toString());

                                // 如果找不到关联关系，自动新增一条记录
                                if (connect == null) {
                                    connect = new OrderSourceConnect();
                                    connect.setUnionCode(skuId.toString());
                                    connect.setName((String)eee.get("skuName"));
                                    connect.setProductNewId(null); // product_new_id为空，后续可以在后台补充
                                    connect.setOrderSourceId(Long.valueOf(szmCOrderMainlianying.getOrdersource()));
                                    connect.setCreateTime(new Date());
                                    connect.setUpdateTime(new Date());

                                    try {
                                        orderSourceConnectMapper.insert(connect);
                                        logger.error("getOrder2 - 自动新增关联关系：ordersource={}, skuId={}, name={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getName());
                                    } catch (Exception insertEx) {
                                        logger.error("getOrder2 - 自动新增关联关系失败：ordersource={}, skuId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, insertEx);
                                    }
                                }

                                if (connect != null && connect.getProductNewId() != null) {
                                    // 根据product_id查询产品价格
                                    SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                                    if (productInfo != null && productInfo.getPrice() != null) {
                                        // 使用price字段而不是sellprice
                                        BigDecimal basePrice = productInfo.getPrice();
                                        Integer quantity = eee.getInteger("skuCount");

                                        // 根据数量计算单位配送费
                                        BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                        // 单品最终单价 = 基础价格 + 单位配送费
                                        BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                        // 总价 = 单品最终单价 * 数量
                                        orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity)).doubleValue();
                                        unitPrice = finalUnitPrice.toString();
                                        totalPrice = orderDetailsProductPrice.toString();

                                        logger.error("getOrder2 - skuId: {}, productId: {}, basePrice: {}, unitDeliveryFee: {}, finalUnitPrice: {}, quantity: {}, totalPrice: {}",
                                            skuId, connect.getProductNewId(), basePrice, unitDeliveryFee, finalUnitPrice, quantity, orderDetailsProductPrice);
                                    } else {
                                        logger.error("getOrder2 - 关联关系存在但product_new_id为空或产品价格为空：ordersource={}, skuId={}, productNewId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getProductNewId());
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("getOrder2 - 计算订单详情价格失败，skuId: {}, ordersource: {}", skuId, szmCOrderMainlianying.getOrdersource(), e);
                            }

                            SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                            smzCOrderDetailLianying.setSource(0);
                            smzCOrderDetailLianying.setProductModelId(null);
                            smzCOrderDetailLianying.setProductSkuname((String) eee.get("skuName"));
                            smzCOrderDetailLianying.setProductSkuimg("");
                            smzCOrderDetailLianying.setOrderProductNum(eee.getInteger("skuCount"));
                            // 设置计算出的价格
                            smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                            smzCOrderDetailLianying.setR1(unitPrice);
                            smzCOrderDetailLianying.setOrderMainId(orderId.toString());
                            smzCOrderDetailLianying.setR5(orderId + "1");//子订单编号
                            smzCOrderDetailLianying.setR4(totalPrice);
                            smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                            smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                            smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                            smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                        }
                        // 通知用户
                        szmBOrderService.jieDanSms(orderId.toString());
                        // 通知商家
                        StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                        if (storeSmsInfo != null) {
                            SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                            if (storeSmsInfo.getResidueNum() > 0) {
                                SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                                if (smsRelevance != null && smsRelevance.getState() == 1) {
                                    String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                                    UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                                    storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                                    storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                                    storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                                    SmsRecord smsRecord = new SmsRecord();
                                    smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                                    SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                                    smsRecord.setContent(smsMaster.getName());
                                    smsRecordMapper.insert(smsRecord);
                                }
                            } else {
                                RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                            }
                        }
                        LoggerUtil.info("新增订单通知");
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("新订单通知");//模块名称
                        storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMainlianying.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                        stringBuffer.append("下了一笔订单，请点击前往处理！");
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                        storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                        storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                        storeMsgMapper.insert(storeMsg);
                        try {
                            // 自动派单送水员
                            Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                            szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                            logger.error("送水员围栏判断，找到送水员id:{}", deliveryUserId);
                        if (deliveryUserId != null) {
                                szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                            }
                        } catch (Exception e) {
                            logger.error("自动派单送水员失败", e);
                        }
                    }
                    pageNow = pageNow + 1;
                    this.getOrder2(start, end, pageNow);
                }
                return new ResultBean().success();
            } else {
                return new ResultBean().error(jsonResult.get("msg"));
            }
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }


    @RequestMapping("getOrder3")
    public ResultBean getOrder3(String start,String end,Integer pageNo) throws Exception {
        int pageNow = pageNo == null ? 1 : pageNo;
//        String startTime = DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER);
//        String endTime = DateUtils.format(DateUtils.addMinutes(new Date(), 10), DateUtils.DATE_TIME_PATTERN_OTHER);
        String jd_param_json = "{\"" +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_begin\":\"" + start + "\",\"") : "") +
                (StringUtils.isNotEmpty(start) ? ("orderStartTime_end\":\"" + end + "\",\"") : "") +
                "pageNo\":\"" + pageNow + "\",\"pageSize\":\"" + pageSize + "\"}";

        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey3);
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        webReqeustDTO.setToken(token3);
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret3);
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", token3);
        params.put("app_key", appKey3);
        params.put("timestamp", StringUtils.isEmpty(start) ? DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) : start);
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);
//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/order/es/query", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            String encryptData = (String) jsonObject.get("encryptData");
            String key = appSecret3.substring(0, 16);
            String iv = appSecret3.substring(16, 32);
            String json = AESUtils.decryptAES(encryptData, key, iv);
            JSONObject jsonResult = JSON.parseObject(json);
            String code1 = jsonResult.get("code").toString();
            if (code1.equals("0")) {
                JSONObject qqqq = JSON.parseObject((String) jsonResult.get("result"));
                JSONArray gggg = qqqq.getJSONArray("resultList");
                if (!CollectionUtil.isEmpty(gggg)) {
                    for (Object o : gggg) {
                        JSONObject wwww = (JSONObject) o;
                        String mobile = (String) wwww.get("buyerMobile");
                        String deliveryStationName = (String) wwww.get("deliveryStationName");
                        String username = (String) wwww.get("buyerFullName");
                        String buyerCityName = (String) wwww.get("buyerCityName");
                        String buyerCountryName = (String) wwww.get("buyerCountryName");
                        String buyerFullAddress = (String) wwww.get("buyerFullAddress");
                        BigDecimal buyerLat = (BigDecimal) wwww.get("buyerLat");
                        BigDecimal buyerLng = (BigDecimal) wwww.get("buyerLng");
                        Integer orderStatus = (Integer) wwww.get("orderStatus");
                        Date orderPreEndDeliveryTime = wwww.getDate("orderPreEndDeliveryTime");
                        Long orderId = (Long) wwww.get("orderId");
                        Integer orderNum = 0;
                        Long storeId = null;
                        // if (buyerLat != null && buyerLng != null) {
                        //     storeId = storeIdUtil.determineByWeiLan(buyerLat, buyerLng, buyerCityName, buyerCountryName, buyerFullAddress, buyerFullAddress,0);
                        // } else {
                            storeId = StoreIdUtil.determineStoreId(buyerCityName,buyerCityName, buyerCountryName, buyerFullAddress);
                        // }
                        Double orderTotalMoney = new BigDecimal((Integer) wwww.get("orderTotalMoney")).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).doubleValue();
                        SzmCOrderMain szmCOrderMain = szmCOrderMainMapper.selectByOrderNum(orderId.toString());
                        if (null != szmCOrderMain) {
                            if (szmCOrderMain.getOrderStatus() >= 1) {
                                // 看看订单状态是不是取消了
                                    if (orderStatus.equals(20010) || orderStatus.equals(20020) || orderStatus.equals(20030) || orderStatus.equals(20040)) {
                                        SmzCOrderReturns smzCOrderReturns = smzCOrderReturnsMapper.selectByOrderNum(orderId.toString());
                                        if (null == smzCOrderReturns) {
                                            smzCOrderReturns = new SmzCOrderReturns();
                                            smzCOrderReturns.setOrderReturnsDelStart(1);
                                            smzCOrderReturns.setProcessstate((orderStatus.equals(20020) || orderStatus.equals(20040)) ? 1 : 0);//退款状态
                                            smzCOrderReturns.setConsigneerealName(szmCOrderMain.getUserName());
                                            smzCOrderReturns.setConsigneetelPhone(szmCOrderMain.getUserPhone());
                                            smzCOrderReturns.setReturnSamount(Double.parseDouble(szmCOrderMain.getR1()));
                                            smzCOrderReturns.setStoreId(Long.parseLong(szmCOrderMain.getR2()));
                                            smzCOrderReturns.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            smzCOrderReturns.setR4(szmCOrderMain.getOrderStatus().toString());
                                            smzCOrderReturns.setOrderMainId(szmCOrderMain.getOrderMainId());
                                            smzCOrderReturns.setOrderDetailsId(orderId.toString());
                                            smzCOrderReturns.setReturnsType("退款");
                                            smzCOrderReturns.setLogisticsDescription("其他");
                                            smzCOrderReturnsMapper.insert(smzCOrderReturns);
                                            szmCOrderMain.setOrderStatus(8);
                                            szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            
                                            StoreMsg storeMsg = new StoreMsg();
                                            storeMsg.setStoreMsgModel("退款/退货通知");//模块名称
                                            storeMsg.setStoreId(Long.parseLong(szmCOrderMain.getR2()));//商户id
                                            storeMsg.setModelUrl("orderAdmin");//模块地址
                                            storeMsg.setUserId(Long.parseLong(szmCOrderMain.getCreateIden()));//用户id
                                            StringBuffer stringBuffer = new StringBuffer();
                                            stringBuffer.append("您的客户 ");
                                            SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMain.getCreateIden()));
                                            if (Validator.isEmpty(szmCUserinfo.getR3())) {
                                                stringBuffer.append(szmCOrderMain.getUserName());
                                            } else {
                                                stringBuffer.append(szmCUserinfo.getR3());
                                            }
                                            stringBuffer.append(" 于");
                                stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));
                                            if (smzCOrderReturns.getReturnsType().equals("退货退款")) {
                                                stringBuffer.append("发起了订单退货申请，请点击前往处理！");
                                            } else {
                                                stringBuffer.append("发起了订单退款，请点击前往处理！");
                                            }
                                            storeMsg.setContent(stringBuffer.toString());//内容
                                            storeMsg.setReadState(0);//已读 1 未读 0
                                            storeMsg.setSource(2);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                                            storeMsg.setDelState(0);//删除状态
                                            storeMsg.setR1("pages/mine/after-sales/after-sales");//小程序路径
                                            storeMsg.setR2(smzCOrderReturns.getOrderDetailsId());//退货id
                                            storeMsgMapper.insert(storeMsg);
                                        } else {
                                            if(orderStatus.equals(20020) ||  orderStatus.equals(20040)){
                                                smzCOrderReturns.setProcessstate(1);//退款状态
                                                smzCOrderReturnsMapper.updateByPrimaryKey(smzCOrderReturns);
                                                szmCOrderMain.setOrderStatus(8);
                                                szmCOrderMainMapper.updateByPrimaryKey(szmCOrderMain);
                                            }
                                        }
                                    }
                                }
                                continue;
                        }
                        // 只有付过款的订单，才能进入
                        if (!orderStatus.equals(31020) && !orderStatus.equals(41000)
                                && !orderStatus.equals(32000) && !orderStatus.equals(33040) && !orderStatus.equals(33060) && !orderStatus.equals(90000)) {
                            continue;
                        }
                        SzmCUser szmCUser = szmCUserMapper.selectByPhone(mobile);
                        if (null != szmCUser) {
                            // if (StringUtils.isNotEmpty(szmCUser.getR2()) && szmCUser.getR2().equals("0")) {
                                szmCUser.setR2(storeId.toString());
                                szmCUser.setStoreId(storeId);
                                szmCUser.setOrdersource(1);
                                szmCUserMapper.updateByPrimaryKey(szmCUser);
                            // }
                        } else {
                            // 创建用户
                            szmCUser = new SzmCUser();
                            szmCUser.setUserMobile(mobile);
                            szmCUser.setUserNickname(username);
                            szmCUser.setR2(storeId.toString());
                            szmCUser.setStoreId(storeId);
                            szmCUser.setBindingTime(new Date());
                            szmCUser.setOrdersource(1);
                            ResultBean resultBean1 = szmCUserService.addUser(szmCUser);
                            if (1 != resultBean1.getCode()) {
                                return resultBean1;
                            }
                        }

                        SzmCUser szmCUserExtra = szmCUserMapper.selectByPhone(mobile);

                        SzmCAddress szmCAddress = new SzmCAddress();
                        szmCAddress.setUserId(szmCUserExtra.getUserId());
                        szmCAddress.setUserName(szmCUserExtra.getUserNickname());
                        szmCAddress.setTelphoneOne(szmCUserExtra.getUserMobile());
//                        szmCAddress.setProvince(oldUser.getProvince());
                        szmCAddress.setCity(buyerCityName);
                        szmCAddress.setArea(buyerCountryName);
                        szmCAddress.setStreet(buyerFullAddress);
                        szmCAddress.setIsDefaultAddress(0);
                        szmCAddress.setState(0);
                        szmCAddress.setR1("0");
                        szmCAddress.setR2("1");

                        szmCAddress.setR5(buyerLat + "," + buyerLng);
                        szmCAddressMapper.insertAddress(szmCAddress);
                        // 开始组建订单

                        JSONArray product = wwww.getJSONArray("product");
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            orderNum += eee.getInteger("skuCount");
                        }
                        //设置商品信息
                        SzmCOrderMain szmCOrderMainlianying = new SzmCOrderMain();
                        szmCOrderMainlianying.setAppkey(appKey3);
                        szmCOrderMainlianying.setApptoken(token3);
                        szmCOrderMainlianying.setZuobiao(buyerLat + "," + buyerLng);
                        szmCOrderMainlianying.setLat(buyerLat);
                        szmCOrderMainlianying.setLon(buyerLng);
                        szmCOrderMainlianying.setYuyuetime(orderPreEndDeliveryTime);
                        szmCOrderMainlianying.setGroup(0);
                        szmCOrderMainlianying.setUpPrice(0D);
                        szmCOrderMainlianying.setRoyalty(0D);
                        szmCOrderMainlianying.setRemind(0);
                        szmCOrderMainlianying.setCdTypeMoney(0D);
                        szmCOrderMainlianying.setCdMoneyType(0);
                        szmCOrderMainlianying.setIsReturn(0);
                        szmCOrderMainlianying.setIsSms(0);
                        szmCOrderMainlianying.setIsForward(1);
                        szmCOrderMainlianying.setIsInvoice(0);
                        szmCOrderMainlianying.setBucketPrice(0D);
                        szmCOrderMainlianying.setYfMoney(0D);
                        szmCOrderMainlianying.setBack(0);
                        szmCOrderMainlianying.setOrderDiscounts(0D);
                        szmCOrderMainlianying.setFreightPayable(0D);
                        szmCOrderMainlianying.setCdMoney(0D);
                        szmCOrderMainlianying.setCreateIden(szmCUserExtra.getUserId().toString());
                        szmCOrderMainlianying.setUserId(szmCUserExtra.getUserId());

                        szmCOrderMainlianying.setCreateTime(new Date());
                        szmCOrderMainlianying.setPayTime(new Date());
                        szmCOrderMainlianying.setOrderNum(orderId.toString());
                        szmCOrderMainlianying.setUserName(username);
                        szmCOrderMainlianying.setUserPhone(mobile);
                        szmCOrderMainlianying.setUserAddress(buyerFullAddress);
                        // szmCOrderMainlianying.setOrderMoney(orderTotalMoney);
                        szmCOrderMainlianying.setOrderMoney(0D);
                        szmCOrderMainlianying.setR1("0");
                        szmCOrderMainlianying.setPayNum(orderId.toString());
                        szmCOrderMainlianying.setOrderStatus(2);
                        szmCOrderMainlianying.setIsReplenishment(0);
                        szmCOrderMainlianying.setUserContent(deliveryStationName);
                        szmCOrderMainlianying.setOrderDelState(0);
                        szmCOrderMainlianying.setR3("0");
                        szmCOrderMainlianying.setR4("[]");
                        szmCOrderMainlianying.setBucketBeans("[]");
                        szmCOrderMainlianying.setR5(orderNum.toString());
                        szmCOrderMainlianying.setR2(szmCUserExtra.getR2());
                        szmCOrderMainlianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                        szmCOrderMainlianying.setPaymentModeId(8L);
                        szmCOrderMainlianying.setDaike(0);
                        szmCOrderMainlianying.setRemind(1);
                        szmCOrderMainlianying.setOrdersource(1);
                        
                        // 根据ordersource获取OrderSource数据，并将settlement_cycle设置到mark字段
                        try {
                            OrderSource orderSource = orderSourceService.selectByPrimaryKey(1);
                            if (orderSource != null && orderSource.getSettlementCycle() != null) {
                                szmCOrderMainlianying.setMark(orderSource.getSettlementCycle());
                            }
                        } catch (Exception e) {
                            logger.error("获取订单来源结算周期失败", e);
                        }
                        
                        szmCOrderMainMapper.insertCancelOrder(szmCOrderMainlianying);
                        
                        for (Object object : product) {
                            JSONObject eee = (JSONObject) object;
                            Long skuId = eee.getLong("skuId");

                            // 根据ordersource和skuId查询关联的product_id并计算价格
                            Double orderDetailsProductPrice = 0D;
                            String unitPrice = "0";
                            String totalPrice = "0";

                            try {
                                // 查询关联关系
                                OrderSourceConnect connect = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                                    Long.valueOf(szmCOrderMainlianying.getOrdersource()), skuId.toString());

                                // 如果找不到关联关系，自动新增一条记录
                                if (connect == null) {
                                    connect = new OrderSourceConnect();
                                    connect.setUnionCode(skuId.toString());
                                    connect.setName((String)eee.get("skuName"));
                                    connect.setProductNewId(null); // product_new_id为空，后续可以在后台补充
                                    connect.setOrderSourceId(Long.valueOf(szmCOrderMainlianying.getOrdersource()));
                                    connect.setCreateTime(new Date());
                                    connect.setUpdateTime(new Date());

                                    try {
                                        orderSourceConnectMapper.insert(connect);
                                        logger.error("getOrder3 - 自动新增关联关系：ordersource={}, skuId={}, name={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getName());
                                    } catch (Exception insertEx) {
                                        logger.error("getOrder3 - 自动新增关联关系失败：ordersource={}, skuId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, insertEx);
                                    }
                                }

                                if (connect != null && connect.getProductNewId() != null) {
                                    // 根据product_id查询产品价格
                                    SzmCProductNew productInfo = szmCProductNewMapper.selectByPrimaryKey(connect.getProductNewId());
                                    if (productInfo != null && productInfo.getPrice() != null) {
                                        // 使用price字段而不是sellprice
                                        BigDecimal basePrice = productInfo.getPrice();
                                        Integer quantity = eee.getInteger("skuCount");

                                        // 根据数量计算单位配送费
                                        BigDecimal unitDeliveryFee = calculateDeliveryFee(productInfo.getDeliveryfee(), quantity);

                                        // 单品最终单价 = 基础价格 + 单位配送费
                                        BigDecimal finalUnitPrice = basePrice.add(unitDeliveryFee);

                                        // 总价 = 单品最终单价 * 数量
                                        orderDetailsProductPrice = finalUnitPrice.multiply(new BigDecimal(quantity)).doubleValue();
                                        unitPrice = finalUnitPrice.toString();
                                        totalPrice = orderDetailsProductPrice.toString();

                                        logger.error("getOrder3 - skuId: {}, productId: {}, basePrice: {}, unitDeliveryFee: {}, finalUnitPrice: {}, quantity: {}, totalPrice: {}",
                                            skuId, connect.getProductNewId(), basePrice, unitDeliveryFee, finalUnitPrice, quantity, orderDetailsProductPrice);
                                    } else {
                                        logger.error("getOrder3 - 关联关系存在但product_new_id为空或产品价格为空：ordersource={}, skuId={}, productNewId={}",
                                            szmCOrderMainlianying.getOrdersource(), skuId, connect.getProductNewId());
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("getOrder3 - 计算订单详情价格失败，skuId: {}, ordersource: {}", skuId, szmCOrderMainlianying.getOrdersource(), e);
                            }

                            SmzCOrderDetails smzCOrderDetailLianying = new SmzCOrderDetails();
                            smzCOrderDetailLianying.setSource(0);
                            smzCOrderDetailLianying.setProductModelId(null);
                            smzCOrderDetailLianying.setProductSkuname((String) eee.get("skuName"));
                            smzCOrderDetailLianying.setProductSkuimg("");
                            smzCOrderDetailLianying.setOrderProductNum((Integer) eee.getInteger("skuCount"));
                            // 设置计算出的价格
                            smzCOrderDetailLianying.setOrderDetailsProductPrice(orderDetailsProductPrice);
                            smzCOrderDetailLianying.setR1(unitPrice);
                            smzCOrderDetailLianying.setOrderMainId(orderId.toString());
                            smzCOrderDetailLianying.setR5(orderId + "1");//子订单编号
                            smzCOrderDetailLianying.setR4(totalPrice);
                            smzCOrderDetailLianying.setIsForward(1);//是否转单 0是 1否
                            smzCOrderDetailLianying.setStoreId(Long.parseLong(szmCUserExtra.getR2()));
                            smzCOrderDetailLianying.setOrderid(szmCOrderMainlianying.getOrderMainId());
                            smzCOrderDetailsMapper.insertCancelOrder(smzCOrderDetailLianying);
                        }
                        // 通知用户
                        szmBOrderService.jieDanSms(orderId.toString());
                        // 通知商家
                        StoreSmsInfo storeSmsInfo = storeSmsInfoMapper.selectByStoreId(szmCOrderMainlianying.getStoreId());
                        if (storeSmsInfo != null) {
                            SzmCStoreApplyFor szmCStoreApplyFor = szmCStoreApplyForMapper.selectStoreId(szmCOrderMainlianying.getStoreId());
                            if (storeSmsInfo.getResidueNum() > 0) {
                                SmsRelevance smsRelevance = smsRelevanceMapper.selectByStoreAndMaster(szmCOrderMainlianying.getStoreId(), 1l);//发起退款
                                if (smsRelevance != null && smsRelevance.getState() == 1) {
                                    String template = "【水站买】：您有一条待处理的订单，请到订单管理及时处理。";

                                    UtilSMS.sendSMS(szmCStoreApplyFor.getStoreTel(), template);
                                    storeSmsInfo.setResidueNum(storeSmsInfo.getResidueNum() - 1);
                                    storeSmsInfo.setPastNum(storeSmsInfo.getPastNum() + 1);
                                    storeSmsInfoMapper.updateByPrimaryKey(storeSmsInfo);
                                    SmsRecord smsRecord = new SmsRecord();
                                    smsRecord.setStoreId(szmCOrderMainlianying.getStoreId());
                                    SmsMaster smsMaster = smsMasterMapper.selectByPrimaryKey(1l);
                                    smsRecord.setContent(smsMaster.getName());
                                    smsRecordMapper.insert(smsRecord);
                                }
                            } else {
                                RemindSMS.remindSMS(szmCStoreApplyFor.getStoreTel());
                            }
                        }
                        LoggerUtil.info("新增订单通知");
                        StoreMsg storeMsg = new StoreMsg();
                        storeMsg.setStoreMsgModel("新订单通知");//模块名称
                        storeMsg.setStoreId(szmCOrderMainlianying.getStoreId());//商户id
                        storeMsg.setModelUrl("orderAdmin");//模块地址
                        storeMsg.setUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));//用户id
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append("您的客户 ");
                        SzmCUserinfo szmCUserinfo = szmCUserinfoMapper.selectBrUserId(Long.parseLong(szmCOrderMainlianying.getCreateIden()));
                        if (Validator.isEmpty(szmCUserinfo.getR3())) {
                            stringBuffer.append(szmCOrderMainlianying.getUserName());
                        } else {
                            stringBuffer.append(szmCUserinfo.getR3());
                        }
                        stringBuffer.append(" 于");
                        stringBuffer.append(cn.hutool.core.date.DateUtil.formatDateTime(szmCOrderMainlianying.getCreateTime()));
                        stringBuffer.append("下了一笔订单，请点击前往处理！");
                        storeMsg.setContent(stringBuffer.toString());//内容
                        storeMsg.setReadState(0);//已读 1 未读 0
                        storeMsg.setSource(1);//来源 1  待处理订单，2退款，3 购买水票，4退桶，5借物资，6 还物资，7 使用水票
                        storeMsg.setDelState(0);//删除状态
                        storeMsg.setR1("pages/orderAdmin/orderAdmin?type=1");//小程序路径
                        storeMsg.setR2(szmCOrderMainlianying.getOrderNum());//id
                        storeMsg.setR3(szmCOrderMainlianying.getOrderMainId().toString());//id
                        storeMsgMapper.insert(storeMsg);
                        
                        try {
                            // 自动派单送水员
                            Long deliveryUserId = storeIdUtil.determineByWeiLanDeliveryUser( szmCOrderMainlianying.getLat(), 
                            szmCOrderMainlianying.getLon(),szmCOrderMainlianying.getUserAddress(),szmCOrderMainlianying.getStoreId());
                            logger.error("送水员围栏判断，找到送水员id:{}", deliveryUserId);
                        if (deliveryUserId != null) {
                                szmBOrderService.selectDeliveryId(szmCOrderMainlianying.getOrderNum(), deliveryUserId, 0D, 0D, 0D, 0D);
                            }
                        } catch (Exception e) {
                            logger.error("自动派单送水员失败", e);
                        }
                    }
                    pageNow = pageNow + 1;
                    this.getOrder3(start, end, pageNow);


                }
                return new ResultBean().success();
            } else {
                return new ResultBean().error(jsonResult.get("msg"));
            }
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }

    @RequestMapping("OrderSerllerDelivery")
    public ResultBean OrderSerllerDelivery(String orderNum,String reaaaappkey) throws Exception {
        JddjKeyVo jddjKeyVo = jddjKeyVoMap.get(reaaaappkey);
        String jd_param_json = "{\"orderId\":\"" + orderNum + "\",\"operator\":\"管理员\"}";
        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(jddjKeyVo.getAppKey());
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        webReqeustDTO.setToken(jddjKeyVo.getToken());
        webReqeustDTO.setV("1.0");
        String sign = SignUtils.getSignByMD5(webReqeustDTO, jddjKeyVo.getAppSecret());
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", jddjKeyVo.getToken());
        params.put("app_key", jddjKeyVo.getAppKey());
        params.put("timestamp", DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/bm/open/api/order/OrderSerllerDelivery", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            return new ResultBean().success();
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }
    }

    @RequestMapping("orderCancelOperate")
    public ResultBean orderCancelOperate(String orderNum,String reaaaappkey,boolean isAgreed,String operator,String remarks) throws Exception {
        JddjKeyVo jddjKeyVo = jddjKeyVoMap.get(reaaaappkey);
        String jd_param_json =isAgreed ?  ("{\"orderId\":\"" + orderNum + "\",\"isAgreed\":\" + isAgreed + \",\"operator\":\"管理员\"}") 
        : ("{\"orderId\":\"" + orderNum  + "\",\"isAgreed\":\" + isAgreed +  + \"\",\"remarks\":\" + remarks + \",\"operator\":\"管理员\"}");
        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(jddjKeyVo.getAppKey());
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        webReqeustDTO.setToken(jddjKeyVo.getToken());
        webReqeustDTO.setV("1.0");
        String sign = SignUtils.getSignByMD5(webReqeustDTO, jddjKeyVo.getAppSecret());
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", jddjKeyVo.getToken());
        params.put("app_key", jddjKeyVo.getAppKey());
        params.put("timestamp", DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/ocs/orderCancelOperate", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            return new ResultBean().success();
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }
    }

    @RequestMapping("deliveryEndOrder")
    public ResultBean deliveryEndOrder(String orderNum,String reaaaappkey) throws Exception {
        JddjKeyVo jddjKeyVo = jddjKeyVoMap.get(reaaaappkey);
        String jd_param_json = "{\"orderId\":\"" + orderNum + "\",\"operPin\":\"管理员\",\"operTime\":\"" + DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER) + "\"}";
        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(jddjKeyVo.getAppKey());
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        webReqeustDTO.setToken(jddjKeyVo.getToken());
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, jddjKeyVo.getAppSecret());
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", jddjKeyVo.getToken());
        params.put("app_key", jddjKeyVo.getAppKey());
        params.put("timestamp", DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);

//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/ocs/deliveryEndOrder", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            return new ResultBean().success();
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }

//skuId -> {Long@19152} 2371912527
    @RequestMapping("queryListBySkuIds")
    public ResultBean queryListBySkuIds(Long skuId) throws Exception {

        String jd_param_json = "{\"skuIds\":\"" + skuId+ "\"}";
        WebRequestDTO webReqeustDTO = new WebRequestDTO();
        webReqeustDTO.setApp_key(appKey);
        webReqeustDTO.setFormat("json");
        webReqeustDTO.setJd_param_json(jd_param_json);
        webReqeustDTO.setTimestamp(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        webReqeustDTO.setToken(token);
        webReqeustDTO.setV("1.0");

        String sign = SignUtils.getSignByMD5(webReqeustDTO, appSecret);
        System.out.println("md5 sign:" + sign);
        Map<String, Object> params = new HashMap<>();
        params.put("token", token);
        params.put("app_key", appKey);
        params.put("timestamp", DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN_OTHER));
        params.put("format", "json");
        params.put("sign", sign);
        params.put("v", "1.0");
        params.put("jd_param_json", jd_param_json);

//        String result = RequestUtil.post("https://openapi.jddj.com/djapi/order/es/query", params);
        String result = HttpUtil.sendSimplePostRequest("https://openapi.jddj.com/djapi/order/queryListBySkuIds", params);
        JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        if (code.equals("0")) {
            return new ResultBean().success(jsonObject);
        } else {
            return new ResultBean().error(jsonObject.get("msg"));
        }

    }

    @RequestMapping("/djsw/newOrder")
    public ResultBean djswnewOrder(String result) throws Exception {
        logger.error(result);
        return new ResultBean().error(result);
    }

    /**
     * 根据deliveryfee配置和数量计算配送费
     * @param deliveryFeeJson deliveryfee字段的JSON字符串，格式：[{"max":1,"fee":"9.5"},{"max":9,"fee":"9"},{"max":null,"fee":"8"}]
     * @param quantity 商品数量
     * @return 配送费
     */
    private BigDecimal calculateDeliveryFee(String deliveryFeeJson, Integer quantity) {
        if (StringUtils.isBlank(deliveryFeeJson) || quantity == null || quantity <= 0) {
            return BigDecimal.ZERO;
        }

        try {
            JSONArray deliveryFeeArray = JSON.parseArray(deliveryFeeJson);
            if (deliveryFeeArray == null || deliveryFeeArray.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 遍历配送费配置，找到匹配的区间
            for (Object item : deliveryFeeArray) {
                JSONObject feeConfig = (JSONObject) item;
                Object maxObj = feeConfig.get("max");
                String feeStr = feeConfig.getString("fee");

                if (StringUtils.isBlank(feeStr)) {
                    continue;
                }

                // 如果max为null，表示无上限，直接返回该费率
                if (maxObj == null) {
                    return new BigDecimal(feeStr);
                }

                // 如果数量小于等于max值，返回该费率
                Integer maxValue = Integer.valueOf(maxObj.toString());
                if (quantity <= maxValue) {
                    return new BigDecimal(feeStr);
                }
            }

            // 如果没有找到匹配的区间，返回0
            return BigDecimal.ZERO;

        } catch (Exception e) {
            logger.error("解析配送费配置失败，deliveryFeeJson: {}, quantity: {}", deliveryFeeJson, quantity, e);
            return BigDecimal.ZERO;
        }
    }

}