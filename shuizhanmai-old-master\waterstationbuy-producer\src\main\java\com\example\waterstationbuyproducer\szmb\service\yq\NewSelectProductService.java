/**
 * @姓名 杨强
 * @版本号 1.1.4
 * @日期 2019/6/25
 */
package com.example.waterstationbuyproducer.szmb.service.yq;

import com.example.waterstationbuyproducer.util.ResultBean;
import org.apache.ibatis.executor.ResultExtractor;

public interface NewSelectProductService {
    /**
     * 查看字典表商品
     * @return
     */
    ResultBean selectProduct(Long classId,Integer index);

    /**
     * 查看商家已经添加的字典商品
     * @param storeId
     * @param classId
     * @return
     */
    ResultBean selectProductByStoreId(Long storeId,Long classId,Integer pageIndex);


    /**
     * 查看商家已经添加的字典商品(pc)
     * @param storeId
     * @param classId
     * @param pageIndex
     * @param productName
     * @param state
     * @return
     */
    ResultBean selectProductByStoreIdPc(Long storeId,Long classId,Integer pageIndex,String productName,Integer state,Long brandId,String searchKeywords,Integer isMultiKeywordSearch);

    /**
     * 查看spu商品的信息
     * @param spuId
     * @return
     */
    ResultBean selectShopSpu(Long spuId);

    /**
     * 查看spu商品的信息
     * @param spuId
     * @param source
     * @return
     */
    ResultBean newSelectShopSpu(Long spuId,Integer source);

    /**
     * 查看子商品的信息
     * skuId
     */
    ResultBean selectShopSkuDeatil(Long skuId);

    /**
     * 查看商品的信息
     * @param productName
     * @return
     */
    ResultBean selectListProduct(String productName);

    /**
     * 查询子商品
     * @param classId
     * @param name
     * @return
     */
    ResultBean selectListProductModel(Long classId,String name,Long storeId);


    /**
     * 分类查看商品
     * @param classId
     * @param name
     * @param storeId
     * @return
     */
    ResultBean selectListByProductName(Long classId,String name,Long storeId);

    /**
     * 进货调整
     * @param storeId
     * @param shopClassId
     * @param state
     * @return
     */
    ResultBean selectProductListInventory(Long storeId, Integer shopClassId,Integer state);
    /**
     * 进货调整(PC)
     * @param storeId
     * @param shopClassId
     * @return
     */
    ResultBean selectProductListInventoryPc(Long storeId, Long shopClassId,Long brandId,String productName,Integer pageNo,Integer pageSize);

    /**
     * 查询该店铺分类和品牌
     * @param storeId
     * @return
     */
    ResultBean selectStoreClassAndBrand(Long storeId);

    /**
     * 库存管理-库存盘点(PC)
     * @param storeId
     * @param shopClassId
     * @return
     */
    ResultBean selectProductInventoryPdPc(Long storeId, Long shopClassId,Long brandId,String productName,Integer pageNo,Integer pageSize);

    /**
     * 库存管理-库存统计(PC)
     * @param storeId
     * @param shopClassId
     * @return
     */
    ResultBean selectProductInventoryTjPc(Long storeId, Long shopClassId,Long brandId,String productName,Integer pageNo,Integer pageSize);


    /**
     * 查看商品标签
     * @return
     */
    ResultBean selectProductLable();

    /**
     * 查询该店铺的分类和品牌
     * @param storeId
     * @return
     */
    ResultBean selectStoreAllBrand(Long storeId);
}
